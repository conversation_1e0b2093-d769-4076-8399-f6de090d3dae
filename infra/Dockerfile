FROM maven:3.9.8-eclipse-temurin-21-alpine AS build
WORKDIR /app
COPY src ./src
COPY pom.xml settings2.xml ./
RUN mvn package --settings settings2.xml

FROM eclipse-temurin:21.0.4_7-jre-alpine as release
COPY --from=build /app/target/lease-yaqeen-service-0.0.1.jar  service.jar
ENV JVM_OPTS="-XshowSettings:vm \
              -XX:+PrintFlagsFinal \
              -Xms300m \
              -Xmx500m \
			  -XX:+ShowCodeDetailsInExceptionMessages \
			  -XX:+AlwaysActAsServerClassMachine \
              -XX:+UseStringDeduplication \
              -XX:FreqInlineSize=425 \
              -XX:MaxInlineLevel=15 \
              -XX:+HeapDumpOnOutOfMemoryError"

CMD java $JVM_OPTS -jar service.jar