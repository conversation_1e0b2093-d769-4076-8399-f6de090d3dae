Component: lumi-core
SubComponent: yaqeen-business

readinessProbePath: /actuator/health
livenessProbePath: /actuator/health

livenessProbe:
  initialDelaySeconds: 120
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1
readinessProbe:
  initialDelaySeconds: 120
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1


istio:
    enabled: true
    virtualService:
        enabled: true
        destinationRule:
            trafficPolicy:
                tls:
                    mode: DISABLE

    gateway:
        enabled: true
        host: "*"
        name: core-api-gateway
        match:
            - uri: /yaqeen-core-service/
              rewrite: /
    customHeaders:
        enabled: true
        headers:
            X-Forwarded-Prefix: /yaqeen-core-service/

secret_variable:
  enabled: true
  data:
    - SPRING_DATASOURCE_PASSWORD
    - PETROMIN_PASSWORD
    - SAFEROAD_PASSWORD

environment_variable:
  enabled: true
  data:
    CAR_PRO_SERVICE: "http://lumi-core-carpro-service.core.svc.cluster.local"
    PETROMIN_SERVICE: "https://mobility.ntsc.app/api/"
    PETROMIN_USERNAME: "<EMAIL>"

deployment_custom_annotations:
  enabled: true
  annotations:
    prometheus.io/path: "/actuator/prometheus"
    prometheus.io/port: "80"
    prometheus.io/scrape: "true"

