environment: dev
service:
  type: ClusterIP
  scheme: HTTP
  port: 80

#Kong Configs
kongService:
  enabled: true
  type: ExternalName
  origin: istio-ingressgateway.istio-system.svc.cluster.local

apiKongIngress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/preserve-host: "false"
    konghq.com/https-redirect-status-code: "301"
  hosts:
    api-dev.lumirental.com:
      - /yaqeen-core-service/
    lumi-api:
      - /yaqeen-core-service/

hpa:
  enabled: true
  targetCPUUtilizationPercentage: 80
  TargetAvgMemoryUtilization: 80
replicaCount: 2
maxReplicas: 4

resources:
  requests:
    cpu: 100m
    memory: 700Mi
  limits:
    cpu: 1000m
    memory: 1Gi

environment_variable:
  enabled: true
  data:
      SERVER_PORT: "80"
      ACTIVE_PROFILE: "dev"
      KEYCLOAK_URL: "https://keycloak.dev.lumirental.com"
      SWAGGER_HOST: "api-dev.lumirental.com/yaqeen-core-service"
      DB_TIMEOUT: "20000"
      DB_MIN_IDLE: "2"
      DB_IDLE_TIME: "10000"
      DB_MAX_LIFE: "30000"
      DLT_AUTO_START: "true"
      SPRING_PROFILES_ACTIVE: "dev"
      APP_NAME: "lumi-yaqeen-core-service"
      SWAGGER_CONTEXT: "/yaqeen-core-service"
      SPRING_DATASOURCE_URL: "*************************************************************************************************************************************************************************************************************"
      SPRING_DATASOURCE_USERNAME: "lumi-core-yaqeen-business"
      KAFKA_HOST: "lumi-lease-kafka-cluster:9092"
      KAFKA_DEFAULT_HOST: "lumi-kafka-cluster:9092"
      KAFKA_LISTENER_CONCURRENCY: "1"
      KAFKA_PARTITION: "2"
      KAFKA_REPLICATION: "1"
      SPRING_REDIS_HOST: "lumi-core-redis"
      SPRING_REDIS_PORT: "6379"