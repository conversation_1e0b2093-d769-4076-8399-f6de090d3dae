<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <import class="ch.qos.logback.classic.encoder.PatternLayoutEncoder" />
    <import class="ch.qos.logback.core.ConsoleAppender" />
    <import class="ch.qos.logback.core.rolling.RollingFileAppender" />
    <import class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy" />
    <import class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy" />
    <springProperty scope="context" name="app_name" source="info.application.name"/>
    <springProperty scope="context" name="env" source="spring.profiles.active"/>

    <springProfile name="!local">
        <appender name="STDOUT" class="ConsoleAppender" >
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <pattern>
                        <pattern>
                            {
                            "timeStamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}",
                            "traceId": "%mdc{traceId}",
                            "spanId": "%mdc{spanId}",
                            "thread": "%t",
                            "logLevel": "%p",
                            "logger": "%logger",
                            "message": "#tryJson{%message}"
                            }
                        </pattern>
                    </pattern>
                </providers>
            </encoder>
        </appender>
        
        <!-- File Appender for Non-Local Profiles -->
        <appender name="FILE" class="RollingFileAppender">
            <file>logs/application.log</file>
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <pattern>
                        <pattern>
                            {
                            "timeStamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}",
                            "traceId": "%mdc{traceId}",
                            "spanId": "%mdc{spanId}",
                            "thread": "%t",
                            "logLevel": "%p",
                            "logger": "%logger",
                            "message": "#tryJson{%message}"
                            }
                        </pattern>
                    </pattern>
                </providers>
            </encoder>
            <rollingPolicy class="TimeBasedRollingPolicy">
                <fileNamePattern>logs/application.%d{yyyy-MM-dd}.log</fileNamePattern>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <triggeringPolicy class="SizeBasedTriggeringPolicy">
                <maxFileSize>100MB</maxFileSize>
            </triggeringPolicy>
        </appender>
    </springProfile>
    <springProfile name="local">
        <appender name="STDOUT" class="ConsoleAppender">
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>${CONSOLE_LOG_THRESHOLD}</level>
            </filter>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level - %msg%n</pattern>
                <charset>${CONSOLE_LOG_CHARSET}</charset>
            </encoder>
        </appender>
        
        <!-- File Appender for Local Profile -->
        <appender name="FILE" class="RollingFileAppender">
            <file>logs/application.log</file>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
            <rollingPolicy class="TimeBasedRollingPolicy">
                <fileNamePattern>logs/application.%d{yyyy-MM-dd}.log</fileNamePattern>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <triggeringPolicy class="SizeBasedTriggeringPolicy">
                <maxFileSize>100MB</maxFileSize>
            </triggeringPolicy>
        </appender>
    </springProfile>

    <root level="INFO">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
    </root>
    <Logger name="reqResLogger" level="INFO" additivity="false" >
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
    </Logger>
    
    <!-- Audit Logger for specific audit logging -->
    <Logger name="com.seera.lumi.yaqeen.service.AuditLogService" level="INFO" additivity="false">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
    </Logger>
</configuration>