<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <property name="now" value="now()" dbms="h2"/>
  <property name="now" value="now()" dbms="mysql, mariadb"/>
  <property name="floatType" value="float4" dbms="postgresql, h2"/>
  <property name="floatType" value="float" dbms="mysql, oracle, mssql, mariadb"/>
  <property name="clobType" value="clob" dbms="h2"/>
  <property name="clobType" value="clob" dbms="mysql, oracle, mssql, mariadb, postgresql"/>
  <property name="uuidType" value="varchar(36)" dbms="h2, mysql, mariadb"/>
  <property name="datetimeType" value="datetime(6)" dbms="mysql, mariadb"/>
  <property name="datetimeType" value="datetime" dbms="oracle, mssql, postgresql, h2"/>
<!-- 
  <include file="/db/changes/20240514010101_added_entity_Multilingual.xml"/>
  <include file="/db/changes/20240514010101_added_entity_AccountAddress.xml"/>
  <include file="/db/changes/20240514010101_added_entity_CorporateCustomer.xml"/>
  <include file="/db/changes/20240514010101_added_entity_CorporateCustomerAccount.xml"/>
  <include file="/db/changes/20240621010101_added_entity_ReplicationJobConfiguration.xml"/> -->
  <include file="/db/changes/20250131010101_added_entity_BusinessAuditEvent.xml"/>
</databaseChangeLog>
