
update `lumi-core-yaqeen-business`.corporate_customer_account set region = null;
update `lumi-core-yaqeen-business`.corporate_customer_account set account_type = null;

update `lumi-core-yaqeen-business`.account_address set  house_no='',city='',country='',zip='';



UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='RENTAL', account_type='Corporate' where carpro_account_id=31632;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30648;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30697;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30705;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30684;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30685;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31685;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30706;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30707;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30713;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30726;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30725;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30727;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30749;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30774;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30779;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30780;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30792;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30775;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30790;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30791;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31797;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30809;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30799;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30810;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30826;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30827;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31839;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30828;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30865;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30866;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30839;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31859;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30862;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30863;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30864;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30868;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30869;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30886;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30888;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30892;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30893;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30895;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30900;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30887;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30889;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30890;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30891;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='North', customer_type='LEASE', account_type='Government' where carpro_account_id=30899;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='North', customer_type='LEASE', account_type='Government' where carpro_account_id=30907;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30908;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30909;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30937;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30939;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30942;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='RENTAL', account_type='Corporate' where carpro_account_id=30943;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30922;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30923;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30953;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30954;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30938;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30940;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30941;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30978;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='North', customer_type='LEASE', account_type='Government' where carpro_account_id=31031;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31035;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31036;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31038;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31000;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Government' where carpro_account_id=31005;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31006;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31068;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31018;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31020;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31021;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31022;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31023;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31085;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31088;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='North', customer_type='LEASE', account_type='Government' where carpro_account_id=31090;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31034;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31037;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31050;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31103;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31104;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31123;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31126;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31131;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31134;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31063;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31154;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31075;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31080;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31184;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='North', customer_type='LEASE', account_type='Government' where carpro_account_id=31185;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31087;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='North', customer_type='LEASE', account_type='Government' where carpro_account_id=31089;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31206;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31105;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31125;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31127;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='North', customer_type='LEASE', account_type='Government' where carpro_account_id=31133;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31135;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='North', customer_type='LEASE', account_type='Government' where carpro_account_id=31232;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31153;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31157;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='South', customer_type='LEASE', account_type='Government' where carpro_account_id=31182;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31188;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31260;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31261;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31231;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31299;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31312;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='North', customer_type='LEASE', account_type='Government' where carpro_account_id=31259;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31269;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31270;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31327;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31271;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31275;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31334;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31339;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31356;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31357;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31358;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31290;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31365;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31372;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31389;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='North', customer_type='LEASE', account_type='Government' where carpro_account_id=31335;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='South', customer_type='LEASE', account_type='Government' where carpro_account_id=31336;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31418;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Government' where carpro_account_id=31421;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Government' where carpro_account_id=31351;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31371;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31448;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31384;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31387;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31388;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31406;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31420;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31432;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31522;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='RENTAL', account_type='Corporate' where carpro_account_id=31469;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31474;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31556;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='South', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31559;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31560;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31523;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31582;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31541;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31557;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31558;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Government' where carpro_account_id=31567;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31568;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31581;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31583;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31615;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31623;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=31624;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10031;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10032;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10034;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10035;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10036;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10038;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10039;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10037;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10042;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10040;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10043;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10046;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10041;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10044;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10049;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10045;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10050;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10047;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10052;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10048;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10051;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10056;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10053;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10055;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10057;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10060;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10058;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10059;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10064;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10062;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10063;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10065;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10066;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10067;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10069;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10071;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10070;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10074;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10072;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10073;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='RENTAL', account_type='Government' where carpro_account_id=10075;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10077;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10076;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10079;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10088;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10083;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10084;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10092;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10085;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10086;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10087;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10099;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10101;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10105;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10107;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10100;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10102;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10108;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10106;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10110;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10109;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10113;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10112;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10114;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10115;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10117;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10116;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10129;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10134;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10135;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10136;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10137;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10147;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10151;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10152;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10158;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10159;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10153;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10160;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10161;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10162;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10157;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=10163;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30002;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30003;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30001;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30015;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30020;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30022;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30018;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30019;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30021;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30028;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30026;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30030;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30031;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30033;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30038;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30039;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30044;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30046;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30048;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Government' where carpro_account_id=30047;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30050;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30056;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30057;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30070;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30072;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30058;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30078;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30059;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30060;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30061;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30062;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30063;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30064;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30069;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30071;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30084;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30085;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30073;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30076;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30086;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30079;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30092;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30082;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30090;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30095;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30091;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30097;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30100;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30101;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30102;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30103;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30113;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30114;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30115;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30118;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30124;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30126;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30125;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30127;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30139;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30140;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30142;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30150;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30137;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30151;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30138;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30143;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30154;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30146;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30162;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='South', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30153;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30166;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30167;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30163;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30179;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30180;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30181;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30182;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30183;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Government' where carpro_account_id=30185;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30192;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Government' where carpro_account_id=30186;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30193;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30194;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30188;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30197;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30189;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30199;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30190;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30200;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Government' where carpro_account_id=30203;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30195;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30204;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30196;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30206;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30198;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30207;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30201;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30210;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30208;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30218;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30238;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30239;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Government' where carpro_account_id=30222;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30235;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30236;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30272;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30274;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30276;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30294;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30295;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30296;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30307;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30308;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Government' where carpro_account_id=30311;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30312;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30313;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30304;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30305;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30306;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30309;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='RENTAL', account_type='Government' where carpro_account_id=30310;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30328;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30330;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30332;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30345;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30344;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30346;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30364;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30363;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30365;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30379;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30378;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30380;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30384;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30382;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Government' where carpro_account_id=30383;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30386;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30387;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30385;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30389;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30416;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30417;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30418;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30422;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30428;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30406;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30419;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30421;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30429;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30442;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30443;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30439;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30459;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30460;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30463;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30465;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30458;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30461;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30462;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30478;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30479;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30491;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30488;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30489;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30508;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30501;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30502;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30503;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30504;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30505;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30506;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30507;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30509;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30541;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30543;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30542;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30562;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central', customer_type='LEASE', account_type='Government' where carpro_account_id=30596;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30098;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Government ' where carpro_account_id=31937;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Government ' where carpro_account_id=31938;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31963;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31942;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31943;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Government ' where carpro_account_id=31950;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31994;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31993;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Government ' where carpro_account_id=31992;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Government ' where carpro_account_id=31978;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31979;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31980;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=30098;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Government ' where carpro_account_id=31937;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Government ' where carpro_account_id=31938;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31963;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31942;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31943;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Government ' where carpro_account_id=31950;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31994;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='East', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31993;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Government ' where carpro_account_id=31992;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='Central ', customer_type='LEASE', account_type='Government ' where carpro_account_id=31978;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31979;
UPDATE `lumi-core-yaqeen-business`.corporate_customer_account SET  region='West', customer_type='LEASE', account_type='Corporate' where carpro_account_id=31980;


UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Aegeon Saudi Arabia Company', ar='شركة ايجيون العربية السعودية(شركة الشخص الواحد)' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31632);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Sahara Net Co. Ltd.', ar='شركة شبكة صحاري المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30648);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Abdulrazaq Mohammed Qanbar Alansari Trading Co.', ar='شركة عبدالرزاق بن محمد بن قنبر الأنصاري التجارية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30697);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ayed Eid Al-Osaimi Geotechnical Co.', ar='شركة عايد عيد العصيمي للجيوتقنية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30705);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ministry of Housing', ar='وزارة الإسكان' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30684);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tamimi Company for Commercial & Maintenance', ar='شركة التميمي للتجارة والصيانة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30685);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Branch of FCC Construction SA', ar='فرع شركة إف سي سي كونستركسيون اس ايه' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31685);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ayed Eid Al-Osaimi Engineering Consulting Office', ar='مؤسسة عايد عيد العصيمي التجارية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30706);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Middle East Investment Company', ar='شركة الشرق الاوسط للاستثمارات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30707);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Middle East Maritime Repair Company,', ar='شركة الشرق الأوسط البحرية للصيانة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30713);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Mutawa Samyong NDT Co. Ltd. (AMSYCO)', ar='شركة المطوع سام يونج لإختبار المواد الصالحة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30726);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Engineering Group International', ar='المجموعةالهندسية السعودية العالمية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30725);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Real Estate Development Fund', ar='صندوق التنمية العقاري' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30727);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Flynas Company Limited', ar='شركة طيران ناس المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30749);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Arabian Logistics Solution Services', ar='شركة الحلول العربية للخدمات اللوجستية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30774);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Fosam Company Limited (Saudi Fosroc)', ar='شركة فوسام المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30779);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Abdul Latif Jameel Oil Limited', ar='شركة عبد اللطيف جميل للزيوت المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30780);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='United Chemicals Co.', ar='شركة الكيماويات المتحدة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30792);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ministry of Interior - Special Forces for Environmental Security', ar='وزارة الداخلية - القوات الخاصة للأمن البيئي' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30775);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Public Investment Fund', ar='صندوق الإستثمارات العامة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30790);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Falak Electronic Equipment & Supplies Co.', ar='شركة الفلك للمعدات والتجهيزات الألكتروني' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30791);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Support & Services Co LTD', ar='شركة الدعم والخدمات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31797);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='My Clinic International Medical Company', ar='شركة مجمع عيادتي الدولية الطبية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30809);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Ports Authority (Mawani', ar='الهيئة العامة للموانئ' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30799);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='COMATEC Saudi Arabia Limited', ar='شركة كوماتيك العربية السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30810);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Emergency Force', ar='قوات الطوارىء الخاصة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30826);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Elite Food Catering Company', ar='شركة النخبة للاطعمه والتموين' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30827);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Hajjan Drilling Company', ar='شركة الهجان لحفر الآبار' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31839);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Sarif Building Materials Company', ar='شركة الصريف لمواد البناء المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30828);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Advanced Measuring Industrial Instruments Co.', ar='شركة القياس المتقدمة الصناعية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30865);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Heba Fire and Safety Equipment Co., Ltd.', ar='شركة هبة لمعدات الحريق والسلامة المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30866);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Salem York Air Conditioning Ltd. - Johnson Controls', ar='شركة ال سالم للتكييف الهواء' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30839);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Shahina International Trucking Company', ar='شركة شاحنة العالمية لتقنية المعلومات شركة شخص واحد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31859);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Wafrah For Industry & Development Company', ar='شركة وفرة للصناعة والتنمية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30862);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='National Center for Environmental Compliance', ar='المركز الوطني للرقابة على الالتزام البيئي' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30863);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ministry of Energy (MOE)', ar='وزارة الطاقة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30864);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='King Abdulaziz Reserve Development Authority', ar='هيئة تطوير محمية الملك عبد العزيز الملكية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30868);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Royal Commission for Jubail and Yanbu - HQ', ar='الهيئة الملكية للجبيل وينبع – المركز الرئيسي' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30869);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Farouk, Mammon Tamer & company.', ar='شركة فاروق ومأمون تمر وشركاهما' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30886);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Arabian Company & Pharmaceutical Medical', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30888);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tamer Arabia Trading Company', ar='شركة تمر العربية التجارية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30892);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Mohammed Saeed Tamr Services Co. Ltd.', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30893);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Munawla Cargo Co.', ar='شركة المناولة للشحن المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30895);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='M.S.AL SUWAIDI HEAVY INDUSTRIES', ar='شركة محمد سالم السويدي للصناعات الثقيلة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30900);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Mohammed Saeid Tamr Transport Services Co. Ltd.', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30887);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Farouk & Maamoun Mohammed Said Tamer Industries Holding Co. Ltd.', ar='شركة فاروق ومأمون محمد سعيد تامر الصناعية القابضة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30889);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Mohammed Saeid Tamr Company for Commercial Investment Ltd.', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30890);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Arabian Japanese Pharmaceutical Co', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30891);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Qurayyat Municipality', ar='بلدية محافظة القريات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30899);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ministry of Health - Hail Health Cluster', ar='وزارة الصحة – تجمع حائل الصحي' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30907);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='National CyberSecurity Authority', ar='الهيئة الوطنية للأمن السيبراني' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30908);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Advance Construction Technology Services', ar='خدمات تكنولوجيا البناء المتقدمة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30909);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Falak Manpower Services Co.', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30937);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Alistithmar for Financial Securities and Brokerage Company', ar='شركة الاستثمار للأوراق المالية والوساطة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30939);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Rentokil Saudi Arabia Limited', ar='شركة رينتوكيل السعودية العربية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30942);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail & Yanbu Industrial Services Co.', ar='شركة الجبيل وينبع لخدمات المدن الصناعية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30943);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='MDS Computer Systems Company', ar='شركه ام دي اس لانظمه الحاسب الالي' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30922);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Majestic International Company', ar='فرع شركة ماجستيك العالمية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30923);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Salam Health Medical Hospital', ar='شركة صحة السلام الطبية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30953);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Trading & Development Co. For Machinery and Equipment Ltd. - REDA', ar='شركة تجارةوتطوير المعدات والالات والتجهيزات - رضا' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30954);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tarteeb Travel & Tourism Company', ar='شركة ترتيب للسفر والسياحة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30938);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Precision Scientific Equipment Company', ar='شركة المعدات العلمية الدقيقة المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30940);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Precision Engineering Works Company', ar='شركة الأعمال الهندسية الدقيقة للصيانة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30941);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='JAL Human Resources Company', ar='شركة جال للموارد البشرية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30978);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Duba Municipality, Tabuk', ar='بلدية محافظة ضباء - أمانة منطقة تبوك' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31031);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Municipality of Al Kharj - Riyadh Municipality', ar='بلدية محافظة الخرج - أمانة منطقة الرياض' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31035);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='DB Engineering & Consulting GmbH', ar='فرع شركة دي بي انجنيرينق اند كونسالتينق جي ام بي اتش' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31036);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='WSP Middle East & Partner Saudi Company for Consulting Engineering', ar='دبليو إس بي ميدل إيست وشريكه السعودية للاستشارات الهندسية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31038);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='M.S.AL SUWAIDI EQUIPMENT AND TRANSPORT COMPANY', ar='شركة محمد سالم السويدي للمعدات والنقل' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31000);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Emirate Of Eastern Province', ar='إمارة المنطقة الشرقية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31005);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Cummins Saudi Arabia Limited', ar='كمنز العربية السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31006);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAUDI TOTAL PETROLEUM PRODUCTS CO', ar='شركة توتال السعودية للمنتجات  البترولية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31068);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Salem York Services Ltd.', ar='شركة آل سا&#65247;&#65250; يورك للخدمات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31018);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Udrive Saudi Arabia Car Rental Company', ar='شركة يو درايف العربية السعودية لتأجير السيارات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31020);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='STATE PROPERTIES GENERAL AUTHORITY', ar='الهيئة العامة لعقارات الدولة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31021);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Arnon Plastic Industries Co. Ltd.', ar='شركة أرنون للصناعات البلاستيكية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31022);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Gerflor Middle East Company Ltd.', ar='شركة جيرفلور الشرق الاوسط' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31023);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='AlUla Development Company', ar='شركة العلا للتطوير شركة شخص واحد مساهمة مقفلة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31085);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAUDI TOURISM AUTHORITY STA', ar='الهيئة السعودية للسياحة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31088);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ASIR REGION MUNICIPALITY', ar='أمانة منطقة عسير' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31090);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Shawaya House Company', ar='شركة بيت الشواية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31034);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Middle East Development Company Ltd. - Medco', ar='شركة الشرق الاوسط للتنمية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31037);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Zakher Marine Saudi Co. Ltd.', ar='شركة زاخر مارين السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31050);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='C.A.T. International L.L.C', ar='شركة كات انترناشنال المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31103);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Branch of FCC Construction Co.Temporary', ar='فرع شركة اف سي سي كونستركسيون مؤقتة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31104);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='MUNICIPALITY OF HAYANIYA AND BARAK', ar='بلدية الحيانية والبراك' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31123);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='GERMANISCHER LLOYD INDUSTRIAL SERVICES CO LTD', ar='ششركة جير مانشر لويد العربية السعودية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31126);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='The Public Authority for Food Security', ar='الهيئة العامة للأمن الغذائى' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31131);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Altaamiair Almotagadaimah LLC', ar='شركة التعمير المتقدمه' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31134);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Anabeeb Industrial Services Co. Ltd.', ar='شركه انابيب للخدمات الصناعيه ا لمحدوده' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31063);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Habitas Al Ula Company for Hotel', ar='شركة هابيتاس العلا الفندقية شركة شخص واحد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31154);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Export-Import Bank', ar='بنك التصدير والاستراد السعودى' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31075);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al-Hilweh Municipality ', ar='بلدية الحلوة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31080);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Gulf Steel Factory Company', ar='شركة مصنع فولاذ الخليج للصناعة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31184);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Nassfa Municipality', ar='بلدية الناصفة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31185);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='National Development Fund', ar='صندوق التنمية الوطني' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31087);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='TATHLEETH MUNICIPALITY', ar='بلدية محافظة تثليث' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31089);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Branch of Samsung C and T Corporation', ar='فرع شركة سامسونج سي اند تي كوربوريشن' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31206);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='JAWA GECAT EQUIPMENT RENTAL COMPANY', ar='شركة جواء جيكات لتأجير المعدات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31105);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='FINE HYGIENIC PAPER CO LTD', ar='شركة الورق الصحي المحدوده' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31125);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='FAST CONSORTIUM COMPANY LTD', ar='شركة ائتلاف فاست المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31127);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='UNIVERSITY OF HAFR AL-BATIN ', ar='جامعة حفر الباطن' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31133);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ZAKAT, TAX  AND CUSTOMS AUTHORITY', ar='هيئة الزكاة والضريبة والجمارك' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31135);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Zaloum Municipality', ar='بلدية زلوم' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31232);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Banyan Ashar For Hospitality Services', ar='بنيان عشار للخدمات الفندقية شركة شخص واحد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31153);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al-Qassim Health Cluster', ar='مجمع صحى القصيم' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31157);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jizan Economic City', ar='مدينة جيزان الاقتصادية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31182);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='National Security Services Company', ar='الشركة الوطنية للخدمات الأمنية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31188);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Future Lifts Company Limited', ar='شركة مصاعد المستقبل للمصاعد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31260);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Mejdaf Al Khaleej Trading Company', ar='شركة مجداف الخليج للتجارة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31261);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Branch of Aqualia Inc. SA', ar='فرع شركة اكواليا إنتك اس ايه' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31231);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='NABD Logistics Service Company', ar='شركة نبض للخدمات اللوجستية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31299);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Gulf Brands for Fast Food (Popeyes)', ar='شركة العلامة الخليجية للوجبات السريعة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31312);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Wadi Al-Dawasir', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31259);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tourism Development Fund', ar='صندوق التنمية السياحي' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31269);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ministry of Foreign Affairs Central office', ar='وزارة الخارجية - الديوان العام' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31270);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Neom Lease', ar='شركة نيوم' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31327);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Municipality of Wadi Bin Hashbal', ar='بلدية وادي بن هشبل' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31271);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Gulf Riyadah Company Limited', ar='شركة ريادة الخليج المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31275);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Post', ar='مؤسسة البريد السعودي' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31334);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Branch of  Dogus Insaat ve Ticaret Anonim Sirketi Company', ar='فرع شركة دوغوس انسات في تيكارت انونيم سي' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31339);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Four Torrey Aircraft Management Company', ar='شركة فور توري لادارة وتشغيل الطائرات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31356);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Rain Municipality', ar=' بلدية الرين ' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31357);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Arabian Establishment for Trading , shipping and Petroleum Company', ar='المؤسسة العربية للتجارة والملاحه والاعمال البترولية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31358);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Nora Driving Company', ar='شركة نورة لتعليم القيادة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31290);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Aecom Arabia Ltd. Co. (Lease)', ar='شركة ايكوم العربية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31365);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='The National Agricultural Services Co.', ar='الشركة الوطنية للخدمات الزراعية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31372);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Rawdat Sudair Municipality', ar='بلدية روضة سدير' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31389);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al-Aweqila Municipality', ar='بلدية العويقيلة  - أمانة الحدود الشمالية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31335);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jazan University', ar='جامعة جازان' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31336);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Albaik food system Company', ar='شركة البيك للانظمة الغذائية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31418);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ministry of Water and Agriculture - Dammam', ar='وزارة البيئة و المياه والزراعة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31421);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='The General Authority for the Affairs of the Grand Mosque and the Prophet’s Mosque', ar='الهيئة العامة للعناية بشؤون المسجد الحرام والمسجد النبوي' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31351);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Shubah municipality', ar='بلدية الشعبة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31371);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='The General Administration for Medical Services (GAMA )', ar='الإدارة العامة للخدمات الطبية (جاما)' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31448);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='DELTA Agthia Manufacturing Company', ar='شركة دلتا الأغذية للتصنيع' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31384);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Arabian Oil Co. - Aramco', ar='شركة الزيت العربية السعودية -ارامكو' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31387);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Arabian Oil Co - Aramco', ar='شركة الزيت العربية السعودية -ارامكو' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31388);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al-Ghat  municipality', ar='بلدية الغاط' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31406);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ministry of Water and Agriculture - Riyadh', ar='وزارة البيئة و المياه والزراعة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31420);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Resaa Business Services compnay', ar='شركة رساء لخدمات الاعمال' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31432);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Budaiya Municipality ', ar='بلدية البديع' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31522);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='MBC Media Saudi Company Limited', ar='شركة إم بي سي ميديا السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31469);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al-Arabiya Media Shabaka Company is a one-person company', ar='شركة شبكة العربية الاعلامية شركة شخص واحد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31474);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ministry of Interior & PSD 120 Vehicles', ar='وزارة الداخلية ومديرية الأمن العام' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31556);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Najran Health 56 Vehicles', ar='الشئون الصحية بمنطقة نجران' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31559);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ministry of Municipal and Rural Affairs and Housing- Momrah 190 Vehicles', ar='وزارة الشئون البلدية والقروية والإسكان' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31560);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Armed Forces Hospital in Qassim', ar='مستشفى القوات المسلحة بالقصيم' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31523);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Yarda Engineering consultancy office', ar='مكتب اليارده للاستشارات الهندسيه' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31582);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Communications and Information Technology Commission- 2 (CITC -2)', ar='هيئة الاتصالات وتقنية المعلومات-2' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31541);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='National Center for Environmental Compliance 120 Vehicles', ar='المركز الوطني للرقابة على االلتزام البيئي' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31557);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ministry of Environment, Water and Agriculture -Main center 150 Vehicles', ar='وزارة البيئة والمياه والزراعة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31558);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Eastern Province Municipality 180 Vehicles', ar='أمانة المنطقة الشرقية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31567);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Amoudi Beverage Industries', ar='شركة العمودي لصناعة المرطبات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31568);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Futtaim Auto & Machinery Co LLC. -  FAMCO KSA', ar='شركة الفطيم للاليات والمعدات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31581);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Food Specialities for Marketing Services', ar='شركة فود سبيشاليتيز للخدمات التسويقية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31583);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Local Content and Government Procurement Authority 1 Vehicle', ar='هيئة المحتوى المحلي والمشتريات الحكومية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31615);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dalma Tech Company LLC', ar='شركة دلما للتقنية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31623);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Special Forces for Security and Protection ( S.F.S.P ) 80 Vehicles', ar='القوات الخاصة للأمن والحماية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31624);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Gulf Trading Holding Co. Ltd.', ar='شركة الخليج للتجارة القابضة المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10031);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة ائتلاف فاست المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10032);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAUDI BINLADIN GROUP', ar='مجموعة بن لادن السعودية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10034);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركه الرياض للصناعات الغذائيه', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10035);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركه ابرال الدوليه للتجاره والمقاولات', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10036);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة شبكات الخلوية', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10038);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Seera Group Holding', ar='مجموعة سيرا القابضة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10039);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة التقنية للاتصالات والمقاولات والاعمال الكهروميكانيكية', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10037);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='وزارة الاتصالات وتكنولوجيا المعلومات', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10042);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة شبكات الخلوية', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10040);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='UNITED MEDICAL GROUP-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10043);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='DELICIOUS FOOD CO-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10046);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dar Al - Arkan Real Estate Development', ar='دار الاركان للتطوير العقاري' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10041);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='MOBILE SYSTEMS INTERNATIONAL CO', ar='فرع شركة موبايل سيستمز انترناشيونال كونسلتنسي ليمتد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10044);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='GROUP HOMES CREATIVITY CONTRACTING- RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10049);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Mousim Travel & Tours Co. Ltd', ar='شركة الموسم للسفر والسياحه المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10045);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شــركة المصنــع السعـــودي للأوســـاط الحيـــويه .', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10050);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAMSUNG ELECTRONICS BINLEYKS-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10047);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='RASIAT NAJID CO (JED)-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10052);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='WADI LABTA FOR TRADING - RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10048);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SIYAG AL AMAN ESTABLISHMENT FOR PRIVATE CIVIL SECURITY', ar='مؤسسة سياج الأمان للحراسة الأمنية المدنية الخاصة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10051);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='MAWJAH TECHNOLOGY COMPANY-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10056);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='HESHAM AL-SWEDY (JED)-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10053);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ZAHA JEDDAH CO - RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10055);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة كابلات جده المحدوده', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10057);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='JAZAN ECONOMIC CITY-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10060);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='OFFICE OF MOHAMMED ZAKI FARSI ENGINEERING CONSULTANTS-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10058);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ZAHA JEDDAH CO - RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10059);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة موجة التكنولوجيا', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10064);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAWARY ENERGY COMPANY', ar='الصوارى لخدمة الحراسة الامنية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10062);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='NAJDIYAH MARKTING-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10063);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='مؤسسة الشبكات المميزة للتجارة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10065);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh Company for Food Industries', ar='شركه الرياض للصناعات الغذائيه' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10066);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Smart Trucks Contracting Est', ar='مؤسسة الشاحنات الذكية للمقاولات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10067);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة الاتحاد الهندسى السعودية (خطيب و علمى )', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10069);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة سمه الاعمال الدولية للمقاولات', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10071);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='هيئة تنظيم الكهرباء والانتاج المزدوج', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10070);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='HORIZON BUSINESS GOLF CO - RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10074);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SULTAN AL SUBHY (JED)-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10072);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SUPPORT SERVICES CO-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10073);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='وزارة الشؤون الاسلامية والدعوة والارشاد', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10075);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='QUSAY INFORMATION SYSTEMS-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10077);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ASSOCIATION LEGION CRITICAL CARE MEDICINE-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10076);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Seera Group Holding', ar='مجموعة سيرا القابضة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10079);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='BRIDGEWAY SPECIALIZED CONTRACTING CO LTD', ar='شركة طريق الجسر المتخصصة للمقاولات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10088);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ABDULLA HADI AL QAHTANI', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10083);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ZERO SCRATCHES GROGEALZITONIA-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10084);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='PATENTES TALGO COMPANY', ar='فرع شركة باتينتيس تالغو اس ال يو' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10092);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='EGYPT AIR(MOHD ABDULLALH -RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10085);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='EISSA ALI AL HILALI (JED)-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10086);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='THE SPECIALIST GROUP (ALMOKHTAS)-RC', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10087);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='AL EATESAM MODERN MARKETING COMPANY LIMITED', ar='شركة الاعتصام الحديثة للتسويق المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10099);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Steps Business Trading Group', ar='مجموعة خطوات الأعمال التجارية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10101);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة البلاد للاستشارات والحلول', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10105);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='KINNARPS PROJECT SOLUTIONS', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10107);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='EXPRESS FOOD COMPANY [ AL-BAIK ]', ar='شركة المأكولات السريعة المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10100);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='TRAFFIC FINES RECEIVABLES', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10102);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='MITSUI & COMPANY LIMITED', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10108);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAUDI ARABIAN INVESTMENTS COMPANY', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10106);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='AL ALAMIA COMPANY', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10110);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAUDI INDIAN COMPANY FOR INSURANCE', ar='الشركه السعوديه الهندية للتامين التعاوني' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10109);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة الخليج الانمائى للمقاولات', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10113);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SELEX ES SAUDI ARABIA CO. LTD', ar='شركة سيليكس اى اس العربية السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10112);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='مكتب افاق طوى للخدمات التجارية', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10114);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='TUV RHEINLAND ARABIA LLC', ar='شركة تى يو فى راينلاند العربية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10115);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Eram Arabia Limited', ar='شركة إيرام العربية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10117);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Wipro Arabia Limited - Riyadh', ar='شركة ويبرو العربية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10116);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='TASHEEL MODREN SUPPORT SERVICES COMPANY LIMITED', ar='شركه التسهيل الحديثه للاعمال المسانده المحدوده' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10129);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة الموارد للاغذية المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10134);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='سفر صالح القحطاني حسين', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10135);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة طيران ناس المحدودة', ar='NAS AVIATION HOLDING COMPANY' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10136);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='GULF AIR COMPANY', ar='فرع شركة طيران الخليج' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10137);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ALAWI BIN HAMID ALHADDAD COMPANY', ar='شركه علوي حامد الحداد وابنائه' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10147);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Bahra Distribution Company Branch of Bahra for Manufacturing of Electrical Devices', ar='شركة بحرة للتوزيع فرع شركة بحرة لتصنيع الادوات الكهربائية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10151);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='CONSOLIDATED CONTRACTORS COMPANY W.L.L.', ar='شركة اتحاد المقاولين ذ.م.م' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10152);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='الشركة السعودية النيوزيلندية لمنتجات الألبان المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10158);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='فرع شركة الخطوط الجويه القطريه', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10159);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Zamil Group Trade & Services Co. Ltd', ar='شركة مجموعة الزامل للتجارة والخدمات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10153);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAUDI BUSINESS MACHINES CO', ar='الشركة السعودية للحاسبات الالكترونية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10160);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة مكتب الكمال للاستيراد المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10161);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة الروافع السعودية للخدمات المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10162);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Branch of Twentec International', ar='فرع شركة توينتك أنترناشيونال' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10157);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة جولة لتنظيم الرحلات السياحية', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 10163);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Rover Support Services Company', ar='شـركـة روفـر لـلـخـدمـات الـمـسـانـدة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30002);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة عبد اللطيف جميل سمت المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30003);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ABDUL LATIF JAMEEL SUMMIT COMPANY LTD', ar='شركة عبد اللطيف جميل سمت المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30001);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Hemaya Company for security and safety equipment and traders', ar='شركة حماية لمعدات الامن والسلامة والتجار' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30015);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='REZAYAT TRADING CO LTD', ar='شركة رياضيات التجارية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30020);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة الخدمات البحرية المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30022);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='COMPASS OCEAN LOGISTICS COMPANY', ar='شركة كومباس اوشين للشحن' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30018);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة دلمون المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30019);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة الكفاءة المتميزه لخدمات التموين المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30021);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة تسويق منتجات قودي السعودية المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30028);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Elaa Cargo Services', ar='ايلاء الخدمات الشحن' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30026);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='AHMED MOHAMMED SALEH BAESHAN & CO.', ar='شركة احمد محمد صالح باعشن وشركاه' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30030);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Mosafer Airlines', ar='شركة المسافرللطيران' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30031);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة دلمون المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30033);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='AL AHLIA RESTAURANT CO. LTD.', ar='شركة المطاعم الاهلية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30038);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة الشرقية للتجارةوالتعهدات ( مساهمة مقفلة )', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30039);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Daa International Limited (Branch)', ar='فرع شركة دي ايه ايه انترناشونال ليمتد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30044);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='The Saudi Investment Bank', ar='البنك السعودي للاستثمار' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30046);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Public Transport Authority', ar='هيئة النقل العام' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30048);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='General Organization for Railways', ar='المؤسسة العامة للخطوط الحديدية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30047);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAUDI DIYAR CONSULTANTS', ar='الديار السعودية للاستشارات الهندسية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30050);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Agricultural Development Co., Ltd.', ar='شركة التنمية الزراعية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30056);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='DESERT HILLS FOR VETERINARY SERVICE', ar='شركه تلال الصحراء للخدماتالبيطريه المحد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30057);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='مؤسسة العميري للمقاولات العامة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30070);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة جواء للموارد البشرية مساهمة مقفلة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30072);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='TANMIAH FOODS COMPANY', ar='شركة التنمية الغذائية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30058);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='وزارة الصحة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30078);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Supreme Foods Processing Company LTD.', ar='شركة تصنيع الاغذيه الممتازة المحدوده' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30059);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة صن سيتي للمشاريع السعودية', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30060);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jawlah Tours and Travels', ar='شركة جولة لتنظيم الرحلات السياحية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30061);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Seera Group Holding', ar='مجموعة سيرا القابضة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30062);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='WARTSILA ENERGY CONTRACTING CO', ar='شركة وارتسيلا لمقاولات الطاقه المحدوده' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30063);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Boecker Public Health Saudia', ar='شركة بويكر للصحة العامة السعودية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30064);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='AljabrTalky Saudi Co. Ltd.', ar='شركة الجبر تالكي السعودية  المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30069);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAUDI HADAYATH COMPANY', ar='شركة هداية السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30071);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='RICI COMPANY LIMITED', ar='شركة آر أي سي أي المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30084);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='KINAN INTERNATIONAL REAL ESTATE DEVELOPMENT COMPANY', ar='شركة كنان الدولية للتطوير العقارى' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30085);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='السادة  الهيئة العامة للأرصاد وحماية البيئة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30073);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='OTIS ELEVATOR COMPANY S.A LIMITED', ar='شركة مصاعد أوتيس العربية السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30076);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة اينيشيال العربية السعودية المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30086);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='AL HAMRANI - FUCHS PETROLEUM SAUDI ARABIA LTD.', ar='شركة الحمرانى فوكس البترولية العربية السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30079);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tamimi Global Company Ltd- For Services and Mainte', ar='شركة التميمي للخدمات والصيانة العالمية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30092);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Master Builders Solutions Saudi Arabia Company', ar='شركة ماستر بيلدرز سولوشنز السعودية العربية للصناعة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30082);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Nokia Solutions & Networks Al Saudia Limited', ar='شركة نوكيا سوليوشنز اند نتويركس السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30090);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ATLAS INDUSTRIAL EQUIPMENT CO', ar='شركة اتلس للمعدات الصناعية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30095);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شــركــة عـــبـــر الـخـلـيــج الـقـابـض', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30091);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة عبر الخليج لخدمات الصمامات المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30097);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='BOUYGUES BATIMENT INTERNATIONAL', ar='شركة بويج باتيمنت انترناشيونال' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30100);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='RB HILTON SAUDI ARABIA LTD.', ar='شركة ار بي هيلتون العربيه السعوديه المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30101);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Siemens Mobility GmbH Branch', ar='&#65235;رع &#65207;ر&#65243;&#65172; &#65203;&#64510;&#65251;&#65255;س &#65251;و&#65169;&#64510;&#65248;&#64510;&#65175;&#65266; &#65183;&#65266; إم &#65169;&#65266; إ&#65175;ش' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30102);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة إيرام للإنتاج الإعلامي', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30103);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Zamil Food Industries Co. Ltd', ar='شركة الزامل للصناعات الغذائية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30113);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة يوكوجاواالعربية السعودية', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30114);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة يوكوجاواللخدمات العربية السعودية', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30115);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAUDI CARRIER SERVICE COMPANY', ar='الشـركـه السـعوديـه لخـدمـة كاريــر' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30118);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAUDI ARABIAN AIRLINES CATERING', ar='شركة الخطوط السعوديه للتموين' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30124);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='GULF INTERNATIONAL ELECTRIC POWER COMPANY LIMITED', ar='شركة الخليج العالمية للطاقة الكهربائية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30126);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='NATIONAL SCIENTIFIC COMPANY LIMITED', ar='الشركة العلمية الوطنية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30125);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ELEVATORS SYSTEMS CONTRACTING COMPANY', ar='شركة نظم المصاعد للمقاولات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30127);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Creative Telecom Company', ar='شركة الاتصال المستحدث' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30139);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='MINISTRY OF ISLAMIC AFFAIRS', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30140);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='معهد الجبيل التقني', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30142);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ZAMIL INTERNATIONAL INFORMATION SERVICES CO. LTD.', ar='شركة الزامل العالمية لخدمات المعلومات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30150);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Professional Mechanical Repair Services Co. Ltd', ar='شركة خدمات الاصلاح الميكانيكي المتخصصة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30137);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='KARANFIL FOOD INDUSTRY, CLEANING, CONTRACTING & AUTO INDUSTRIES CO', ar='فرع شركة قرانفيل للصناعات الغذائية والنظافة والمقاولات والسيارات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30151);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='KONE AREECO LTD', ar='شركة كوني اريكو المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30138);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ARABIAN INTERNATIONAL STEEL STRUCTURE.', ar='الشركة العربية الدولية للإنشاءات الحديدية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30143);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Pioneer Brands Limited Co.', ar='شركة العلامات الرائدة المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30154);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة بتروناش العربية المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30146);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Cooperheat Saudi Arabia Co LTD.', ar='شركة كوبرهيت العربيه السعوديه المحدوده' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30162);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Abdul Hadi Abdullah Al-Qahtani & Sons Co.', ar='شركة عبدالهادي عبدالله القحطاني واولاده لصناعة المرطبات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30153);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة أنظمة القياس والتحكم الصناعي', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30166);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Presidency of State Security', ar='رئاسة أمن الدولة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30167);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='الهيئة العامة للترفيه', ar='General Entertainment Authority' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30163);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='جامعة الملك سعود', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30179);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='DEHLAWI OPTICAL INDUSTRIAL CO LTD', ar='شركة مصنع الدهلوي للنظارات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30180);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='HALWANI BROS COMPANY', ar='شركة حلواني إخوان' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30181);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Abdul Qadir Suleiman Al-Khuraiji & Partners Advertising Co.', ar='شركة عبدالقادر سليمان الخريجي وشركاه للدعاية والإعلان' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30182);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAUDI PUBLIC TRANSPORT COMPANY (SAPTCO)', ar='الشركة السعودية للنقل الجماعي - سابتكو' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30183);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Yanbu Governorate Municipality', ar='بلدية محافظة ينبع' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30185);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia', ar='شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30192);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Irrigation Organization - SIO', ar='المؤسسة العامة للري' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30186);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة - جازان - 05', ar='Tecnicas Reunidas Saudia - Jazan EPC-5' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30193);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة- - جازان 10/11', ar='Tecnicas Reunidas Saudia - Jizan EPC 10/11' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30194);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia', ar='شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30188);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia - TRSSC Technical Office', ar='شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة - المكتب الفني' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30197);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia', ar='شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30189);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia - INITEC', ar='INITEC - شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30199);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia', ar='شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30190);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia - INDUSTRIAL', ar='INDUSTRIAL - شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30200);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='وزارة الشؤون البلدية والقروية - أمانة منطقة الجوف', ar='Al-Jouf Municipality Secretariat' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30203);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia - JAZAN COMMON', ar='شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة- - جازان كومون' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30195);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='وزارة العدل - مكتب تحقيق الرؤية', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30204);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia - IbnZahr', ar='شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة - ابن زهر' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30196);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Palladium Limited Company', ar='شركة بلاديوم المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30206);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia - ENERGY', ar='ENERGY - شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30198);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ARABIAN DESAR CONTRACTING', ar='شركة دسار العربية للمقاولات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30207);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia - PETROKEMYA', ar='PETROKEMYA - شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30201);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ALAHLI SPORTS CLUB', ar='النادي الأهلي الرياضي' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30210);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='الشركة العربية الدولية للنقليات والمعدات الثقيلة المحدودة', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30208);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SETE ENERGY SAUDIA FOR INDUSTRIAL PROJECTS LTD.', ar='شركة سيتي اينرجي السعودية للمشاريع الصناعية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30218);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='الهيئة العامة للاعلام المرئى والمسموع', ar='General Commission for Audio Visual Media' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30238);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Nesma Security Company', ar='شركة نسما للحراسات الأمنية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30239);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Municipality Of Yanbu Al Nakhl', ar='بلدية ينبع النخل' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30222);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Food Concepts Company Ltd.', ar='شركة مفاهيم الغذاء المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30235);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Arabian Thermal Aire Industries Co. Ltd', ar='شـركـة ثـيـرمـال إيـر الصنـاعيـه العـربيـه المحـدوده' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30236);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Arabian Agricultural Services Company (ARASCO)', ar='الشركة العربية للخدمات الزراعية  - اراسكو' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30272);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='JOTUN SAUDI CO. LTD.', ar='شـركة جـوتـن الســعودية المحـدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30274);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='The Red Sea Development Company', ar='شركة البحر الأحمر للتطوير شركة شخص واحد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30276);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Rapid Saudi Arabia Co. Ltd.', ar='شركة رابيد العربية السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30294);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='International Tube and Conduit Company Ltd.', ar='شركة مصنع انابيب ومجاري الكهرباء الدوليه المحدوده' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30295);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Aster Sanad Hospital For Medical Care', ar='شركة سند الرحمة للرعاية الطبية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30296);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Alemar International for Agrochemicals and Veterinary (ARASCO - ALEMAR)', ar='(الاعمار العالمية للكيماويات والبيطرة (اراسكو - الإعمار' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30307);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Middle East Food Solutions Company (ARASCO - MEFSCO)', ar='(شركة الشرق الأوسط لحلول الأغذية (اراسكو - مِفسكو' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30308);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='بلدية محافظة رابغ', ar='Municipality of Rabigh' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30311);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Amha Catering Company', ar='شركة امهى للتموين' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30312);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='PRESIDENCY OF STATE SECURITY - INVESTIGATION', ar='رئاسة أمن الدولة - مباحث' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30313);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Branch of Arabian Agricultural Services Company (ARASCO - FOOD)', ar='(فرع الشركة العربية للخدمات الزراعية (اراسكو - للأغذية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30304);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ARASCO Feed and Concentrate Factory (ARASCO - FEED)', ar='(مصنع اراسكو للاعلاف والمركزات (أراسكو - الأعلاف' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30305);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Branch of Arabian Agricultural Services Company (ARASCO - LOGISTICS)', ar='(فرع الشركة العربية للخدمات الزراعية (اراسكو - لوجيستيكس' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30306);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Inspection & Diagnosis Analysis Company (ARASCO - IDAC)', ar='(شركة المعاينة والتشخيص والتحاليل المخبري (اراسكو - أيداك' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30309);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Special Affairs of Crown Prince Office', ar='الشؤون الخاصة لسمو ولي العهد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30310);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ghassan N.Pharaon Co. General Hospital', ar='شركة مستشفى غسان نجيب فرعون العام' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30328);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ASK Gypsum Factory Ltd.', ar='شركة مصنع أسك للجبس المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30330);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Advanced Tarweej For Business Services Ltd. Co.', ar='شركة الترويج المتطورة لخدمات الاعمال المحدوده' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30332);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Red Stone Contracting Est.', ar='مؤسسة العقيق الاحمر للمقاولات العامة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30345);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='JAL International Company Limited', ar='شركة جال الدولية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30344);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Networks & System Integration S.A Co. Ltd. (NESIC)', ar='شركة دمج الشبكات والانمظة المعلوماتية العربية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30346);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='United Catering Company', ar='الشركة المتحدة للتجهيزات الغذائية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30364);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SHELL FISHERIES TRD CO.', ar='شركة اسماك الصدفة المحدودة للتجارة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30363);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='الهيئة العامة للفضاء', ar='SAUDI SPACE COMMISSION' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30365);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Modern Electronics Company Limited', ar='الــشــركــه الالـكـترونـيـه الـحديـثـه' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30379);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi EMCOR Company', ar='شركة امكور السعودية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30378);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Zamil Travel Agency', ar='شركة مجموعة الزامل القابضة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30380);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='مدينة الملك فهد الطبية', ar='King Fahad Medical City - KFMC' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30384);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ministry of Defence - Royal Saudi Air Force', ar='وزارة الدفاع - القوات الجوية الملكية السعودية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30382);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='أمانة محافظة جدة', ar='Jeddah Municipality' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30383);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='VF Services Saudi Ltd.', ar='شركة في إف السعودية للخدمات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30386);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Tamimi and Maintenance Services Global', ar='شركة التميمي للخدمات والصيانه العالميه' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30387);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='United Company for Coffee and Chocolate Trading Ltd. - MOCHACHINO', ar='شركة المتحدون لتجارة البن والشوكولاته المحدوده - موكاتشينو' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30385);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Qeemah And Dukan For Groessries Company Ltd.', ar='شركة قيمة ودكان للتموينات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30389);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dar Al Markabat Rent A Car', ar='دار المركبة لتأجير السيارات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30416);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Ceramic Company', ar='شركة الخزف السعودية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30417);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Hungry Bunny Company', ar='شركة الأرنب الجائع المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30418);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh Municipality', ar='أمانة منطقة الرياض' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30422);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Afaq Food for Services', ar='شركة أفاق الغذاء للخدمات التجارية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30428);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Velosi Saudi Arabia Co. Ltd.', ar='شركة فيلوسي العربية السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30406);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='AlWoustah Food Services Company Ltd.', ar='شركة الوسطى للخدمات الغذائية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30419);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Wafi Al Takamul International for Food Products Ltd', ar='شركة الوافي التكامل الدولية للمواد الغذائية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30421);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Hassan Ghazi Ibrahim Shaker Co.', ar='شركه الحسن غازي ابراهيم شاكر' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30429);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dar Al Riyadh Consultants', ar='مكتب دار الرياض للاستشارات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30442);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ABANA Enterprises Group Co.', ar='شركه مجموعه ابانا للمشاريع' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30443);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Geidea Technology Co.', ar='شركة جيديا للتقنية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30439);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='INTERMEDIATE CHEMICALS COMPANY', ar='شركة الكيماويات الوسيطة المحدوده' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30459);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Mohammed S.Alsuwaidi Services Co.', ar='شركة محمد سالم السويدي للخدمات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30460);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAUDI METHYL ACRYLATE COMPANY', ar='شركة ميثاكريلات السعودية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30463);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='ABDULLA NASS & PARTNERS CO. LTD.', ar='شركة عبدالله ناس وشركاه المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30465);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='M.S.AL SUWAIDI INDUSTRIAL SERVICES CO. LTD.', ar='شركة محمد سالم السويدي للخدمات الصناعيه المحدوده' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30458);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='AL KHOZAMA TRANSPORT COMPANY', ar='شركة الخزامة للنقل' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30461);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Special Forces for Environmental Security', ar='القوات الخاصة للأمن البيئي' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30462);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia', ar='شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30478);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Mafad Trading Company - Cone Zone', ar='شركة مفاد التجارية - كون زون' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30479);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='A branch of Fr. Lürssen Werft GmbH & Co. KG in the Kingdom of Saudi Arabia', ar='فرع شركة إف آر لورسين ويرفت جي أم بي اتش اند سي او كي جي في المملكة العربية السعودية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30491);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Anthos Company Ltd.', ar='شركة انثوس السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30488);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Munir Abdullah Al Munif Plastic Pipes and Fittings Factory Company', ar='شركة مصنع منير عبدالله المنيف للانابيب البلاستيكية و مستلزماتها' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30489);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Bazy Trading and Contracting Company Ltd.', ar='شركة بازي للتجارة والمقاولات المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30508);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Testing Equipment Specialist Team Co.', ar='شركة الفنيون المتخصصون لفحص المعدات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30501);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Technology and Security Comprehensive Control Co. Ltd.(TAHAKOM)', ar='الشركة السعودية للتحكم التقني والأمني الشامل المحد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30502);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Tecnicas Reunidas Saudia - DUQM', ar='شركة تيكنيكاس ريونيداس السعودية للخدمات والمقاولات المحدودة - الدقم' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30503);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='M.S.AL SUWAIDI HOLDING CO.', ar='شركة محمد سالم السويدي القابضة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30504);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ahad Althara Ltd. Co.', ar='شركة عهد الثراء المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30505);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Abeer Medical Company Ltd.', ar='شركة مراكز العبير الطبية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30506);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='FMC Technologies Saudi Arabia Ltd.', ar='شركة إف إم سي تكنولوجيز السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30507);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jotun Powder Coatings Saudi Arabia Ltd.', ar='شركة جوتن باودر كوتنغز العربية السعودية المحدودة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30509);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudi Arabian Glass Co. Ltd.', ar='الشركة السعودية العربية للزجاج' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30541);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Ministry of Interior - Public Security Department', ar='وزارة الداخلية - ألأمن العام ادارة المنافسات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30543);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='M.S.AL SUWAIDI SCAFFOLDING SERVICES & ACCESS SOLUTIONS', ar='شركة محمد سالم السويدي لخدمات السقالات وحلول وسائل الوصول' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30542);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saudia Dairy & Food Stuff Co. - SADAFCO.', ar='الشركة السعوديه لمنتجات الالبان والاغذية - سدافكو' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30562);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Communications and Information Technology Commission - CITC', ar='هيئة الاتصالات وتقنية المعلومات' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30596);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='PAN GULF STEEL', ar='شركة عبر الخليج للحديد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 30098);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Development Authorities support center', ar='مركز دعم هيئات التطوير' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31937);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Majmaah University', ar='جامعة المجمعة' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31938);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Cara Logistic Company', ar='شركة كارا للخدمات اللوجستية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31963);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='SAT Transportation Company', ar='شركة سات للنقل' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31942);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Integrated Services Engineering Consulting  Company (ISEC)', ar='شركة الخدمات التكاملية للاستشارات الهندسية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31943);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Public Security - Logistics and Supply -350 Vehicles', ar='الأمن العام - الامداد والتموين' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31950);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Pro''s Discount', ar='' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31994);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Hempel paints Saudi Arabia W.L.L', ar='شركة اصباغ همبل العربيه السعوديه شركة شخص واحد' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31993);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Zaloum Municipality 8 Vehicles', ar='بلدية زلوم' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31992);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='General Ports Authority(Mawani  -111 Vehicles', ar='الهيئة العامة للموانىء' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31978);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Arabian Petroleum Supply Company - APSCO', ar='الشركة العربية لتجارة المواد البترولية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31979);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Sanabel International Trading Company', ar='شركة سنابل العالمية التجارية' where id=(select name_id from `lumi-core-yaqeen-business`.corporate_customer_account where carpro_account_id = 31980);





UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31632);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30648);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30697);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30705);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30684);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30685);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31685);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30706);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30707);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30713);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30726);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30725);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30727);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30749);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30774);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30779);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30780);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30792);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30775);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30790);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30791);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31797);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30809);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30799);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30810);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30826);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30827);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Saihat', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31839);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30828);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30865);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30866);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30839);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31859);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30862);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30863);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30864);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30868);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30869);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30886);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30888);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30892);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30893);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30895);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30900);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30887);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30889);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30890);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30891);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Qurayyat', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30899);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Hail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30907);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30908);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30909);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30937);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30939);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30942);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30943);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30922);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30923);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30953);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30954);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30938);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30940);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30941);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30978);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Duba', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31031);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Kharj', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31035);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31036);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31038);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31000);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31005);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31006);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31068);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31018);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31020);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31021);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31022);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31023);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31085);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31088);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Asir', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31090);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31034);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31037);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31050);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dhahran', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31103);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31104);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31123);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31126);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31131);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31134);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31063);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Ula', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31154);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31075);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Kharj', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31080);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31184);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al-Jawf', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31185);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31087);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Asir', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31089);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31206);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31105);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31125);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31127);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Hafar Al-Batin', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31133);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31135);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al-Jawf', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31232);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Ula', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31153);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Qassim', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31157);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jazan', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31182);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31188);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31260);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31261);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31231);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31299);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31312);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Wadi Al-Dawasir', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31259);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31269);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31270);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31327);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31271);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31275);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31334);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31339);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31356);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31357);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31358);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31290);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31365);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31372);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31389);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='North', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31335);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jazan', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31336);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31418);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31421);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Makah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31351);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31371);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31448);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31384);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31387);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31388);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31406);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31420);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31432);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31522);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31469);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31474);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31556);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Najran', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31559);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31560);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Qassim', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31523);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Buraydah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31582);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31541);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31557);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31558);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31567);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31568);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31581);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31583);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31615);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31623);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31624);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10031);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10032);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10034);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10035);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10036);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10038);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10039);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10037);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10042);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10040);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10043);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10046);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10041);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10044);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10049);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10045);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10050);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10047);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Makah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10052);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10048);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10051);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10056);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10053);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10055);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10057);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10060);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10058);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10059);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10064);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10062);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10063);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10065);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10066);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10067);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10069);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10071);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10070);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10074);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10072);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10073);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10075);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10077);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10076);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10079);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10088);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10083);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10084);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10092);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10085);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10086);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10087);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10099);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10101);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10105);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10107);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10100);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10102);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10108);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10106);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10110);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10109);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10113);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10112);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10114);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10115);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10117);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10116);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10129);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10134);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10135);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10136);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10137);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10147);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10151);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10152);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10158);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10159);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10153);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10160);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10161);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10162);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10157);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 10163);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30002);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30003);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30001);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30015);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30020);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30022);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30018);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30019);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30021);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30028);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30026);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30030);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30031);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30033);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30038);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30039);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30044);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30046);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30048);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30047);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30050);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30056);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30057);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30070);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30072);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30058);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30078);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30059);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30060);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Madina', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30061);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30062);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30063);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30064);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30069);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30071);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30084);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30085);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30073);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30076);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30086);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30079);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30092);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30082);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30090);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30095);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30091);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30097);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30100);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30101);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30102);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30103);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30113);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30114);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30115);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30118);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30124);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30126);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30125);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30127);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30139);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30140);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30142);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30150);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30137);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30151);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30138);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30143);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30154);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30146);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30162);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='KHAMIS MUSHAYT', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30153);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30166);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30167);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30163);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30179);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30180);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30181);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30182);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30183);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Yanbu', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30185);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30192);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al Ahssa', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30186);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30193);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30194);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30188);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30197);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30189);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30199);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30190);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30200);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='AL ZOUF', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30203);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30195);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30204);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30196);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30206);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30198);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30207);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30201);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30210);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30208);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30218);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30238);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30239);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Yanbu', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30222);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30235);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30236);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30272);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30274);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30276);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30294);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30295);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30296);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30307);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30308);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30311);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30312);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30313);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30304);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30305);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30306);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30309);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30310);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30328);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Yanbu', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30330);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30332);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30345);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30344);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30346);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30364);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30363);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30365);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30379);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30378);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30380);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30384);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30382);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30383);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30386);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30387);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30385);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30389);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30416);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30417);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30418);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30422);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30428);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dhahran', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30406);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Qassim', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30419);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30421);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30429);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30442);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30443);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30439);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30459);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30460);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30463);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30465);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30458);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30461);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30462);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30478);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30479);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30491);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30488);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30489);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30508);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30501);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30502);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30503);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30504);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30505);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30506);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30507);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30509);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30541);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30543);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jubail', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30542);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30562);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30596);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 30098);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31937);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31938);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31963);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31942);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Khobar', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31943);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31950);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31994);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Dammam', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31993);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Al-Jawf', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31992);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Riyadh', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31978);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31979);
UPDATE `lumi-core-yaqeen-business`.multilingual SET  en='Jeddah', ar='' where id=(select aa.address_line_name_id from `lumi-core-yaqeen-business`.corporate_customer_account ca inner join `lumi-core-yaqeen-business`.account_address aa on ca.billing_address_id = aa.id where carpro_account_id = 31980);
