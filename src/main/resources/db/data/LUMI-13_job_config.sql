DROP TABLE IF EXISTS `replication_job_config`;

CREATE TABLE `replication_job_config` (
  `job_id` bigint NOT NULL AUTO_INCREMENT,
  `job_name` varchar(255) NOT NULL,
  `source_name` varchar(16) DEFAULT 'CARPRO',
  `job_description` varchar(255) DEFAULT NULL,
  `columns` text,
  `table_names` text,
  `filters` text,
  `partition_column` varchar(255) DEFAULT NULL,
  `batch_size` int DEFAULT NULL,
  `last_x_days` int DEFAULT NULL,
  `kafka_topic_name` varchar(255) DEFAULT NULL,
  `kafka_partition_key` varchar(255) DEFAULT NULL,
  `cache_keys` json DEFAULT NULL,
  `enabled` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`job_id`),
  KEY `IDX_RJC_job_name` (`job_name`)
);

INSERT INTO `replication_job_config` VALUES (1,'SyncMake','CARPRO','This Job will fetch the car make from carpro','cm.CODE as id, cm.NAME as name','DATABASE_NAME.CAR_MAKES cm','1=1','cm.CODE',100,NULL,'SyncMake','id','[\"id\"]',1);
INSERT INTO `replication_job_config` VALUES (2,'SyncModel','CARPRO','This Job will fetch the car model from carpro','cm.CAR_MAKE as makeId, cm.MODEL as modelId, cm.NAME as name, cm.MODEL_VERSION as version, cm.MODEL_SERIES as series, cm.CAR_GROUP as \'group\', cm.CODEX_ID as materialId','DATABASE_NAME.CAR_MODELS cm','1=1','cm.CAR_MAKE, cm.MODEL ',1000,NULL,'SyncModel','makeId','[\"makeId\", \"modelId\"]',1);
INSERT INTO `replication_job_config` VALUES (3,'SyncVehicleMetaData','CARPRO','This Job will fetch the car meta data like year, color, group etc','ct.UNIT_NO as carproVehicleId, ct.LICENSE_NO as plateNo, ct.LICENSE_NO_IN_LOCAL_LANG  as plateNoInAr, ct.CAR_MAKE as carproMakeId, ct.MODEL as carproModelId, ct.MODEL_YEAR as \'year\', ct.F_GROUP as \'group\', ct.COLOR as color, ct.CHASSIS_NO as chassisNo ','DATABASE_NAME.CAR_TECHNICAL ct','1=1','ct.LICENSE_NO',25000,NULL,'SyncVehicleMetaData','plateNo','[\"plateNo\"]',1);
INSERT INTO `replication_job_config` VALUES (4,'SyncCarGroup','CARPRO','This job will fetch the car group from carpro',' cg.grouf_code AS groupId, cg.f_group AS groupCode, cg.sub_group AS subGroup, cg.rank AS rank, cg.vehicle_class AS vehicleClass, cg.vehicle_type AS vehicleType, cg.minimum_age AS minAge, cg.minimum_license_years AS minLicenseYear, cg.allow_inet_reservation AS allowInetReservation, cg.cgr_allow_in_cs AS allowInCs, cg.cgr_allow_in_web AS allowInWeb, cg.cgr_allow_in_client AS allowInClient, cg.cgr_allow_in_portal AS allowInPortal, cg.cgr_allow_in_import AS allowInImport ','DATABASE_NAME.CAR_GROUPS cg','1=1','cg.f_group',500,NULL,'SyncCarGroup','groupCode','[\"groupCode\"]',1);
INSERT INTO `replication_job_config` VALUES (5,'SyncMaintenancePetromin','PETROMIN','This job will sync maintenance data from petromin',NULL,NULL,NULL,NULL,500,1,'SyncMaintenancePetromin','plateNumber','[\"deliveryNoteNumber\", \"plateNumber\"]',1);
INSERT INTO `replication_job_config` VALUES (6,'SyncTrackingSaferoad','SAFEROAD','This Job will fetch tracking records from saferoad',NULL,NULL,NULL,NULL,100,NULL,'SyncTrackingSaferoad','DisplayName','[\"DisplayName\"]',1);
INSERT INTO `replication_job_config` VALUES (7,'SyncVehicleDocument','CARPRO','This Job will fetch vehicle document from carpro','ud.IDNO as idNo, ud.DOC_TYPE as docType, ud.PAGENO as pageNo, ct.LICENSE_NO as licenceNo, ud.FILNAME as fileName, ud.TRAN_DATE as transactionDate, ud.TRAN_TIME as transactionTime','DATABASE_NAME.CAR_TECHNICAL ct inner join DATABASE_NAME.UPLOAD_HDR uh on ct.UNIT_NO =uh.UNIQUE_NO inner join DATABASE_NAME.UPLOAD_DETAILS ud on ud.IDNO =uh.IDNO','1=1','ud.IDNO',25000,NULL,'SyncVehicleDocument','licenceNo','[\"licenceNo\", \"transactionDate\", \"transactionTime\"]',1);
INSERT INTO `replication_job_config` VALUES (8,'SyncVehicleOperationalData','CARPRO','This Job will fetch vehicle operation data like service type, status, odometer reading',' cc.LICENSE_NO as plateNo, cc.FLEET_ASSIGNMENT as serviceType, cc.FLEET_SUB_ASSIGNMENT as subServiceType, cc.CAR_STATUS as vehicleStatus, cc.BRANACH as currentBranch, cc.CURRENT_ODOMETER as odometerReading, cc.PETROL_LEVEL as fuelLevel ','DATABASE_NAME.CAR_CONTROL cc','1=1','cc.LICENSE_NO',25000,NULL,'SyncVehicleOperationalData','plateNo','[\"plateNo\"]',1);
INSERT INTO `replication_job_config` VALUES (9,'SyncVehicleReservationData','CARPRO','This job will fetch vehicle agreement for real time availibity ',' cc.unit_no AS assetId, cc.license_no AS plateNo, sad.checkout_date AS checkOutDate, sad.checkin_date AS checkInDate, sad.check_out_branach AS checkOutBranch, sad.check_in_branach AS checkInBranch ',' DATABASE_NAME.car_control cc INNER JOIN(SELECT r.unit_no, rr.checkout_date, Cast(CONVERT(VARCHAR(8), r.check_in_date, 112) AS DATETIME) + Dateadd(second, r.check_in_time, 0) AS checkin_date, r.check_out_branach, r.check_in_branach FROM DATABASE_NAME.subagreements r INNER JOIN (SELECT unit_no, Max(Cast(CONVERT(VARCHAR(8), check_out_date, 112) AS DATETIME) + Dateadd(second, check_out_time, 0)) AS checkout_date FROM DATABASE_NAME.subagreements WHERE unit_no != \'\' AND check_out_date != \'00000000\' GROUP BY unit_no) AS rr ON r.unit_no = rr.unit_no AND r.check_in_date != \'00000000\' AND r.check_out_date != \'00000000\' AND Cast(CONVERT(VARCHAR(8), r.check_out_date, 112 ) AS DATETIME) + Dateadd(second, r.check_out_time, 0) = rr.checkout_date ) AS sad ON cc.unit_no = sad.unit_no  ',' Year(sad.checkin_date) >= 2024 AND cc.car_status != 7  ','cc.unit_no',25000,NULL,'SyncVehicleReservationData','plateNo','[\"plateNo\"]',1);
INSERT INTO `replication_job_config` VALUES (10,'SyncAgreements','CARPRO','This job will fetch vehicle agreements for lease',' a.AGREEMENT_NO as agreementNo, a.STATUS_CODE as statusCode, a.LAST_SON_NUMBER as lastSonNumber, a.DRIVER_CODE as driverCode, a.CHECK_OUT_DATE as checkOutDate, a.CHECK_IN_DATE as checkInDate, a.CHECK_OUT_BRANACH as checkOutBranch, a.CHECK_IN_BRANACH as checkInBranch, a.DEBITOR_CODE as debitorCode, a.CAR_GROUP as carGroup, sa.UNIT_NO as unitNo, ct.LICENSE_NO as licenseNo, ct.CAR_MAKE as carMake, ct.MODEL as carModel, ct.MODEL_YEAR as carModelYear, d.FIRST_NAME as driverFirstName, d.LAST_NAME as driverLastName, d.INTERNET_ADDRESS as driverEmail ','DATABASE_NAME.AGREEMENTS a left join DATABASE_NAME.SUBAGREEMENTS sa on sa.FATHER_AGREEMENT_NO = a.AGREEMENT_NO and sa.SON_AGREEMENT_NUMBER = a.LAST_SON_NUMBER left join DATABASE_NAME.CAR_TECHNICAL ct on sa.UNIT_NO = ct.UNIT_NO left join DATABASE_NAME.DRIVERS d on a.DRIVER_CODE = d.DRIVER_CODE left join DATABASE_NAME.DEBITORS deb on a.DEBITOR_CODE = deb.DEBITOR_CODE left join DATABASE_NAME.DEBITORS_SECTION2 debs on a.DEBITOR_CODE = debs.DEBITOR_CODE  ','(deb.DEBITOR_CODE = 31327 or deb.LEASE  = \'Y\') and debs.TERMINATION_DATE = \'00000000\' and a.CHECK_IN_DATE >= \'2024\'  ','a.AGREEMENT_NO',25000,NULL,'SyncAgreementsKafkaTopic','agreementNo','[\"agreementNo\"]',1);
