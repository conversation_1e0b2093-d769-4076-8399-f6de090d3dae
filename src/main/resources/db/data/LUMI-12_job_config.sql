DELETE FROM replication_job_config;

INSERT INTO replication_job_config VALUES (1,'SyncMake','This Job will fetch the car make from carpro','cm.CODE as id, cm.NAME as name','DATABASE_NAME.CAR_MAKES cm','1=1','cm.CODE',100,'SyncMake','id','[\"id\"]',1);
INSERT INTO replication_job_config VALUES (2,'SyncModel','This Job will fetch the car model from carpro','cm.CAR_MAKE as makeId, cm.MODEL as modelId, cm.NAME as name, cm.MODEL_VERSION as version, cm.MODEL_SERIES as series, cm.CAR_GROUP as \'group\', cm.CODEX_ID as materialId','DATABASE_NAME.CAR_MODELS cm','1=1','cm.CAR_MAKE, cm.MODEL ',1000,'SyncModel','makeId','[\"makeId\", \"modelId\"]',1);
INSERT INTO replication_job_config VALUES (3,'SyncVehicleMetaData','This Job will fetch the car meta data like year, color, group etc','cc.UNIT_NO as carproVehicleId, cc.LICENSE_NO as plateNo, cc.F_GROUP as \'group\', cc.CAR_MAKE as carproMakeId, cc.MODEL as carproModelId, cc.COLOR as color','DATABASE_NAME.CAR_CONTROL cc','1=1','cc.LICENSE_NO',25000,'SyncVehicleMetaData','plateNo','[\"plateNo\"]',1);
