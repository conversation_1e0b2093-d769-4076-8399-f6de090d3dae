<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">

  <changeSet id="create-table-corporate-customer" author="suryansh.purwar">
    <preConditions onFail="MARK_RAN">
      <not>
        <tableExists tableName="corporate_customer"/>
      </not>
    </preConditions>
    <createTable tableName="corporate_customer">
      <column autoIncrement="true" name="id" type="BIGINT">
        <constraints nullable="false" primaryKey="true" primaryKeyName="pk_corporate_customer"/>
      </column>
      <column name="name_id" type="BIGINT">
        <constraints unique="true" nullable="false" foreignKeyName="FK_CORPORATE_CUSTOMER_ON_NAME"
          references="multilingual(id)"/>
      </column>
      <column name="vat_no" type="BIGINT">
        <constraints nullable="false"/>
      </column>
      <column name="cr_no" type="VARCHAR(255)"/>
    </createTable>
  </changeSet>

</databaseChangeLog>
