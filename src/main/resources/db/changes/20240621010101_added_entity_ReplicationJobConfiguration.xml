<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">

  <changeSet id="create-replication-job-config" author="suryansh.purwar">
    <preConditions onFail="MARK_RAN">
      <not>
        <tableExists tableName="replication_job_config"/>
      </not>
    </preConditions>
    <createTable tableName="replication_job_config">
      <column autoIncrement="true" name="job_id" type="BIGINT">
        <constraints nullable="false" primaryKey="true" primaryKeyName="pk_replication_job_config"/>
      </column>
      <column name="job_name" type="VARCHAR(255)">
        <constraints nullable="false"/>
      </column>
      <column name="job_description" type="VARCHAR(255)">
        <constraints nullable="false"/>
      </column>
      <column name="columns" type="text"/>
      <column name="table_names" type="text"/>
      <column name="filters" type="text"/>
      <column name="partition_column" type="VARCHAR(255)"/>
      <column name="batch_size" type="int"/>
      <column name="kafka_topic_name" type="VARCHAR(255)"/>
      <column name="kafka_partition_key" type="VARCHAR(255)"/>
      <column name="cache_keys" type="JSON"/>
    </createTable>
    <createIndex tableName="replication_job_config" indexName="IDX_RJC_job_name">
      <column name="job_name"></column>
    </createIndex>
  </changeSet>

  <changeSet id="add_column_enabled_table_replication_job_config" author="mohd.danish">
    <addColumn tableName="replication_job_config">
      <column name="enabled" type="boolean">
        <constraints nullable="false"/>
      </column>
    </addColumn>
  </changeSet>

<!--  <changeSet id="insert-job-config-data" author="mohd.danish" >-->
<!--    <sqlFile path="db/data/LUMI-12_job_config.sql"/>-->
<!--  </changeSet>-->

<!--  <changeSet id="insert-job-config-data-4" author="mohd.danish" >-->
<!--    <sqlFile path="db/data/LUMI-13_job_config.sql"/>-->
<!--  </changeSet>-->

<!--  <changeSet id="insert-job-config-data-5" author="mohd.danish" failOnError="false">-->
<!--    <sqlFile path="db/data/LUMI-14_job_config.sql"/>-->
<!--  </changeSet>-->

  <changeSet id="create-replication-job-history" author="mohd.danish">
    <preConditions onFail="MARK_RAN">
      <not>
        <tableExists tableName="replication_job_history"/>
      </not>
    </preConditions>
    <createTable tableName="replication_job_history">
      <column name="id" type="int" autoIncrement="true">
        <constraints primaryKey="true" nullable="false"/>
      </column>
      <column name="job_id" type="int">
        <constraints nullable="false"/>
      </column>
      <column name="total_task" type="int">
        <constraints nullable="false"/>
      </column>
      <column name="total_record" type="int">
        <constraints nullable="false"/>
      </column>
      <column name="start_time" type="DATETIME" >
        <constraints nullable="false"/>
      </column>
    </createTable>
  </changeSet>

  <changeSet id="create-replication-task-history" author="mohd.danish">
    <preConditions onFail="MARK_RAN">
      <not>
        <tableExists tableName="replication_task_history"/>
      </not>
    </preConditions>
    <createTable tableName="replication_task_history">
      <column name="id" type="int" autoIncrement="true">
        <constraints primaryKey="true" nullable="false"/>
      </column>
      <column name="replication_job_id" type="int">
        <constraints nullable="false"/>
      </column>
      <column name="job_id" type="int">
        <constraints nullable="false"/>
      </column>
      <column name="task_id" type="int">
        <constraints nullable="false"/>
      </column>
      <column name="fetched_record" type="int"/>
      <column name="updated_record" type="int"/>
      <column name="job_status" type="varchar(10)"/>
      <column name="remark" type="varchar(256)"/>
      <column name="start_time" type="DATETIME" />
      <column name="end_time" type="DATETIME" />
    </createTable>
  </changeSet>



</databaseChangeLog>
