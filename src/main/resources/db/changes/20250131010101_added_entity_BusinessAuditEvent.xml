<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <changeSet id="20250131010101" author="system">
        <createTable tableName="business_audit_events">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="audit_id" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="user_id" type="VARCHAR(255)"/>
            <column name="action" type="VARCHAR(255)"/>
            <column name="resource_type" type="VARCHAR(255)"/>
            <column name="resource_value" type="VARCHAR(255)"/>
            <column name="description" type="TEXT"/>
            <column name="method_name" type="VARCHAR(255)"/>
            <column name="class_name" type="VARCHAR(255)"/>
            <column name="parameters" type="JSON"/>
            <column name="result" type="JSON"/>
            <column name="trace_id" type="VARCHAR(255)"/>
            <column name="client_id" type="VARCHAR(255)"/>
            <column name="timestamp" type="DATETIME"/>
            <column name="http_method" type="VARCHAR(10)"/>
            <column name="request_uri" type="TEXT"/>
            <column name="user_agent" type="TEXT"/>
            <column name="ip_address" type="VARCHAR(45)"/>
            <column name="success" type="BOOLEAN"/>
            <column name="error_message" type="TEXT"/>
            <column name="execution_time_ms" type="BIGINT"/>
            <column name="created_on" type="DATETIME" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="DATETIME" defaultValueComputed="CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="business_audit_events" indexName="idx_business_audit_events_audit_id">
            <column name="audit_id"/>
        </createIndex>

        <createIndex tableName="business_audit_events" indexName="idx_business_audit_events_user_id">
            <column name="user_id"/>
        </createIndex>

        <createIndex tableName="business_audit_events" indexName="idx_business_audit_events_timestamp">
            <column name="timestamp"/>
        </createIndex>

        <createIndex tableName="business_audit_events" indexName="idx_business_audit_events_resource">
            <column name="resource_type"/>
            <column name="resource_value"/>
        </createIndex>

        <createIndex tableName="business_audit_events" indexName="idx_business_audit_events_action">
            <column name="action"/>
        </createIndex>

        <createIndex tableName="business_audit_events" indexName="idx_business_audit_events_success">
            <column name="success"/>
        </createIndex>
    </changeSet>

</databaseChangeLog> 