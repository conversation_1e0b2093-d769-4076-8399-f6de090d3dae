<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">

  <changeSet id="create-table-corporate-customer-account" author="suryansh.purwar">
    <preConditions onFail="MARK_RAN">
      <not>
        <tableExists tableName="corporate_customer_account"/>
      </not>
    </preConditions>
    <createTable tableName="corporate_customer_account">
      <column autoIncrement="true" name="id" type="BIGINT">
        <constraints nullable="false" primaryKey="true"
          primaryKeyName="pk_corporate_customer_account"/>
      </column>
      <column name="name_id" type="BIGINT">
        <constraints nullable="false" foreignKeyName="FK_CORPORATE_CUSTOMER_ACCOUNT_ON_NAME"
          references="multilingual(id)"/>
      </column>
      <column name="customer_id" type="BIGINT">
        <constraints nullable="false" foreignKeyName="FK_CORPORATE_CUSTOMER_ACCOUNT_ON_CUSTOMER"
          references="corporate_customer(id)"/>
      </column>
      <column name="email" type="VARCHAR(255)"/>
      <column name="phone" type="VARCHAR(255)"/>
      <column name="billing_address_id" type="BIGINT">
        <constraints unique="true" nullable="false"
          foreignKeyName="FK_CORPORATE_CUSTOMER_ACCOUNT_ON_BILLINGADDRESS"
          references="account_address(id)"/>
      </column>
      <column name="carpro_account_id" type="VARCHAR(255)"/>
      <column name="sap_id" type="VARCHAR(255)"/>
      <column name="account_no" type="VARCHAR(255)"/>
      <column name="region" type="VARCHAR(255)"/>
      <column name="account_type" type="VARCHAR(255)"/>
      <column name="is_active" type="BOOLEAN"/>
      <column name="created_on" type="DATETIME"/>
    </createTable>
  </changeSet>

  <changeSet id="update-customer-id-column" author="suryansh.purwar">
      <modifyDataType tableName="corporate_customer_account" columnName="customer_id" newDataType="bigint null"/>
  </changeSet>

  <changeSet id="drop-account-no-column-customer-account" author="suryansh.purwar">
    <dropColumn tableName="corporate_customer_account" columnName="account_no"/>
  </changeSet>

  <changeSet id="add-type-column-customer-account" author="suryansh.purwar">
      <addColumn tableName="corporate_customer_account">
        <column name="customer_type" type="VARCHAR(50)"></column>
      </addColumn>
  </changeSet>
  <changeSet id="index-customer-account-search-column" author="suryansh.purwar">
    <createIndex tableName="corporate_customer_account" indexName="IDX_CCA_FILTER">
      <column name="account_type"></column>
      <column name="region"></column>
      <column name="customer_type"></column>
    </createIndex>
  </changeSet>

  <changeSet id="customer_data_update_yq_190" author="suryansh.purwar">
    <sqlFile path="db/data/customer_account_data_update_02092024.sql"/>
  </changeSet>
</databaseChangeLog>
