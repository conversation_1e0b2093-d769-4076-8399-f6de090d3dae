<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">

  <changeSet id="create-table-account-address" author="suryansh.purwar">
    <preConditions onFail="MARK_RAN">
      <not>
        <tableExists tableName="account_address"/>
      </not>
    </preConditions>
    <createTable tableName="account_address">
      <column autoIncrement="true" name="id" type="BIGINT">
        <constraints nullable="false" primaryKey="true" primaryKeyName="pk_account_address"/>
      </column>
      <column name="house_no" type="VARCHAR(255)">
        <constraints nullable="false"/>
      </column>
      <column name="address_line_name_id" type="BIGINT">
        <constraints unique="true" nullable="false"
          foreignKeyName="FK_ACCOUNT_ADDRESS_ON_ADDRESS_LINE_NAME"
          references="multilingual(id)"/>
      </column>
      <column name="zip" type="VARCHAR(255)"/>
      <column name="city" type="VARCHAR(255)"/>
      <column name="country" type="VARCHAR(255)"/>
    </createTable>
  </changeSet>

</databaseChangeLog>
