<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">

  <changeSet id="create-table-multilingual" author="suryansh.purwar">
    <preConditions onFail="MARK_RAN">
      <not>
        <tableExists tableName="multilingual"/>
      </not>
    </preConditions>
    <createTable tableName="multilingual">
      <column autoIncrement="true" name="id" type="BIGINT">
        <constraints nullable="false" primaryKey="true" primaryKeyName="pk_multilingual"/>
      </column>
      <column name="version" type="INT"/>
      <column name="created_on" type="DATETIME" defaultValueDate="CURRENT_TIMESTAMP"/>
      <column name="updated_on" type="DATETIME" defaultValueDate="CURRENT_TIMESTAMP"/>
      <column name="en" type="VARCHAR(255)"/>
      <column name="ar" type="VARCHAR(255)"/>
    </createTable>
  </changeSet>

  <changeSet id="modify-multilingual-column" author="suryansh.purwar">
    <modifyDataType tableName="multilingual" columnName="en" newDataType="VARCHAR(500)"/>
    <modifyDataType tableName="multilingual" columnName="ar" newDataType="VARCHAR(500)"/>
  </changeSet>

</databaseChangeLog>
