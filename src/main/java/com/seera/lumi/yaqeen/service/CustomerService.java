package com.seera.lumi.yaqeen.service;

import com.seera.lumi.yaqeen.domain.Customer;
import com.seera.lumi.yaqeen.mapper.LeaseCustomerMapper;
import com.seera.lumi.yaqeen.repository.CustomerRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CustomerService {

  @Autowired private CustomerRepository customerRepository;

  @Autowired private LeaseCustomerMapper customerMapper;

  public void saveAll(List<Customer> customerList) {
    customerRepository.saveAll(customerList);
  }

  public void save(Customer customer) {
    customerRepository.save(customer);
  }

  public Optional<Customer> findByVatNo(Long taxOfficeNo) {
    return customerRepository.findByVatNo(taxOfficeNo);
  }
}
