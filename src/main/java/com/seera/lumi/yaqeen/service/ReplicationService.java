package com.seera.lumi.yaqeen.service;

import com.seera.lumi.yaqeen.controller.request.ReplicationJobInfo;
import com.seera.lumi.yaqeen.controller.request.ReplicationTaskInfo;
import com.seera.lumi.yaqeen.controller.resp.ReplicationJobConfigDTO;
import com.seera.lumi.yaqeen.domain.ReplicationJobConfiguration;
import org.apache.coyote.BadRequestException;

public interface ReplicationService {

  ReplicationJobConfigDTO getJobByName(String jobName) throws Exception;

  void executeTask(ReplicationTaskInfo replicationTaskInfo);

  Long saveJobInfo(ReplicationJobInfo request);

  ReplicationJobConfiguration updateConfig(ReplicationJobConfiguration request)
      throws BadRequestException;

  ReplicationJobConfiguration getJobConfigById(Integer jobId) throws BadRequestException;
}
