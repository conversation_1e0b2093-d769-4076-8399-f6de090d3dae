package com.seera.lumi.yaqeen.service;

import static io.micrometer.common.util.StringUtils.isEmpty;

import com.seera.lumi.yaqeen.exception.BusinessErrors;
import com.seera.lumi.yaqeen.exception.BusinessException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class KafkaService {

  private final KafkaAdmin kafkaAdmin;
  private final MessagePublisher<Map<String, Object>> messagePublisher;
  private final DefaultMessagePublisher<Map<String, Object>> defaultMessagePublisher;
  @Value("${spring.kafka.bootstrap-servers}")
  private String bootstrapAddress;
  @Value("${spring.cloud.stream.kafka.binder.minPartitionCount}")
  private Integer partition;
  @Value("${spring.cloud.stream.kafka.binder.replicationFactor}")
  private Integer replication;

  public void createKafkaTopicIfNotExist(String jobTopicName) {
    if (isEmpty(jobTopicName)) {
      throw new BusinessException(BusinessErrors.INVALID_KAFKA_TOPIC);
    }
    try (AdminClient client =
        AdminClient.create(
            Collections.singletonMap(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress))) {
      if (!client.listTopics().names().get().contains(jobTopicName)) {
        log.warn("{} topic not found, creating...", jobTopicName);
        NewTopic jobTopic =
            TopicBuilder.name(jobTopicName).partitions(partition).replicas(replication).build();
        client.createTopics(Collections.singleton(jobTopic));
      }
    } catch (Exception e) {
      throw new BusinessException(BusinessErrors.KAFKA_TOPIC_NOT_FOUND);
    }
  }

  public void publishRecords(
      List<Map<String, Object>> events, String kafkaTopicName, String kafkaPartitionKey) {
    events.forEach(
        event ->
            messagePublisher.publishMessage(
                event,
                kafkaTopicName,
                kafkaPartitionKey != null ? (String) event.get(kafkaPartitionKey) : "",
                ""));
  }

  public void publishRecordsInDefaultKafka(
      List<Map<String, Object>> events, String kafkaTopicName, String kafkaPartitionKey) {
    events.forEach(
        event ->
            defaultMessagePublisher.publishMessage(
                event,
                kafkaTopicName,
                kafkaPartitionKey != null ? (String) event.get(kafkaPartitionKey) : "",
                ""));
  }
}
