package com.seera.lumi.yaqeen.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static java.util.Objects.nonNull;

@Slf4j
@Service
@RequiredArgsConstructor
public class CacheService {

  private final RedisTemplate<String, Object> redisTemplate;
  private final ObjectMapper objectMapper;

  public Object get(String key) {
    return redisTemplate.opsForValue().get(key);
  }

  public <T> T get(String key, TypeReference<T> type) {
    Object object = redisTemplate.opsForValue().get(key);
    return nonNull(object) ? objectMapper.convertValue(object, type) : null;
  }

  public <T> T get(String key, Class<T> type) {
    Object object = redisTemplate.opsForValue().get(key);
    return nonNull(object) ? objectMapper.convertValue(object, type) : null;
  }

  public void put(String key, Object value) {
    redisTemplate.opsForValue().set(key, value);
  }

  public void put(String key, Object value, long timeout, TimeUnit unit) {
    redisTemplate.opsForValue().set(key, value, timeout, unit);
  }
}
