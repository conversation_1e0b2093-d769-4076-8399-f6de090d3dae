package com.seera.lumi.yaqeen.service.impl;

import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.seera.lumi.yaqeen.controller.request.ReplicationJobInfo;
import com.seera.lumi.yaqeen.controller.request.ReplicationTaskInfo;
import com.seera.lumi.yaqeen.controller.resp.ReplicationJobConfigDTO;
import com.seera.lumi.yaqeen.domain.ReplicationJobConfiguration;
import com.seera.lumi.yaqeen.domain.ReplicationJobHistory;
import com.seera.lumi.yaqeen.domain.ReplicationTaskHistory;
import com.seera.lumi.yaqeen.exception.BusinessErrors;
import com.seera.lumi.yaqeen.exception.BusinessException;
import com.seera.lumi.yaqeen.mapper.ReplicationJobMapper;
import com.seera.lumi.yaqeen.repository.ReplicationJobConfigurationRepository;
import com.seera.lumi.yaqeen.repository.ReplicationJobHistoryRepository;
import com.seera.lumi.yaqeen.repository.ReplicationTaskHistoryRepository;
import com.seera.lumi.yaqeen.service.*;
import com.seera.lumi.yaqeen.util.CacheUtil;
import com.seera.lumi.yaqeen.util.DateUtil;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.coyote.BadRequestException;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReplicationServiceImpl implements ReplicationService {

  private final CacheService cacheService;
  private final KafkaService kafkaService;
  private final SafeRoadService saferoadService;
  private final PetrominService petrominService;
  private final CarProService carProService;

  private final ReplicationJobMapper replicationJobMapper;
  private final ReplicationJobConfigurationRepository replicationJobConfigurationRepository;
  private final ReplicationJobHistoryRepository replicationJobHistoryRepository;
  private final ReplicationTaskHistoryRepository replicationTaskHistoryRepository;

  @Override
  public ReplicationJobConfigDTO getJobByName(String jobName) throws Exception {
    return replicationJobMapper.mapReplicationJobConfigToDTO(getJobConfigurationByName(jobName));
  }

  @Override
  public Long saveJobInfo(ReplicationJobInfo request) {
    try {
      ReplicationJobHistory replicationJobHistory =
          replicationJobMapper.mapReplicationJobHistory(request);
      return replicationJobHistoryRepository.save(replicationJobHistory).getId();
    } catch (Exception ex) {
      log.error("Error while saving ReplicationJobInfo in database request : {}", request);
      return 0L;
    }
  }

  @Override
  public ReplicationJobConfiguration updateConfig(ReplicationJobConfiguration request)
      throws BadRequestException {
    Optional<ReplicationJobConfiguration> existingConfig =
        replicationJobConfigurationRepository.findByJobId(request.getJobId());
    if (existingConfig.isPresent()) {
      ReplicationJobConfiguration jobConfiguration =
          replicationJobMapper.updateConfig(existingConfig.get(), request);
      replicationJobConfigurationRepository.save(jobConfiguration);
      return jobConfiguration;
    } else {
      throw new BadRequestException("Config not found");
    }
  }

  @Override
  public ReplicationJobConfiguration getJobConfigById(Integer jobId) throws BadRequestException {
    return replicationJobConfigurationRepository
        .findByJobId(jobId)
        .orElseThrow(() -> new BadRequestException("NotFound"));
  }

  @Override
  public void executeTask(ReplicationTaskInfo taskInfo) {
    LocalDateTime startTime = DateUtil.getCurrentDateTime();
    try {
      Long startTimeInMillis = System.currentTimeMillis();
      String jobName = taskInfo.getJobName();
      Integer taskId = taskInfo.getTaskId();
      ReplicationJobConfiguration jobConfig = getJobConfigurationByName(jobName);

      log.info(
          "Fetching records for Job-name={} Replication-Job-Id={} TaskId={} from {}",
          jobName,
          taskInfo.getReplicationJobId(),
          taskId,
          jobConfig.getSourceName());
      List<Map<String, Object>> responseBody = getData(taskInfo, jobConfig); //
      log.info(
          "Data received Job-name={} Replication-Job-Id={} TaskId={} from {}, Count={}, timeTaken={}",
          jobName,
          taskInfo.getReplicationJobId(),
          taskId,
          jobConfig.getSourceName(),
          responseBody.size(),
          System.currentTimeMillis() - startTimeInMillis);

      List<Map<String, Object>> records = getUpdatedRecords(responseBody, jobConfig);
      log.info(
          "{} records are new/updated out of {} Job-name={} Replication-Job-Id={}",
          records.size(),
          responseBody.size(),
          jobName,
          taskInfo.getReplicationJobId());

      kafkaService.createKafkaTopicIfNotExist(jobConfig.getKafkaTopicName());
      if (jobName.equals("SyncEmployee"))
        kafkaService.publishRecordsInDefaultKafka(
            records, jobConfig.getKafkaTopicName(), jobConfig.getKafkaPartitionKey());
      else
        kafkaService.publishRecords(
            records, jobConfig.getKafkaTopicName(), jobConfig.getKafkaPartitionKey());
      saveTaskInfo(
          taskInfo.getReplicationJobId(),
          taskInfo.getJobId(),
          taskInfo.getTaskId(),
          responseBody.size(),
          records.size(),
          "Success",
          "",
          startTime,
          DateUtil.getCurrentDateTime());

    } catch (Exception exception) {
      log.error(
          "Error in executing task for Job-name={} Replication-Job-Id={} TaskId={}, Exception {}",
          taskInfo.getJobName(),
          taskInfo.getReplicationJobId(),
          taskInfo.getTaskId(),
          exception.getMessage());
      saveTaskInfo(
          taskInfo.getReplicationJobId(),
          taskInfo.getJobId(),
          taskInfo.getTaskId(),
          null,
          null,
          "Failed",
          exception.getMessage().substring(0, 250),
          startTime,
          DateUtil.getCurrentDateTime());
    }
  }

  private List<Map<String, Object>> getData(
      ReplicationTaskInfo taskInfo, ReplicationJobConfiguration jobConfig)
      throws JsonProcessingException {
    return switch (jobConfig.getSourceName()) {
      case "CARPRO" -> carProService.getData(taskInfo.getTaskId(), jobConfig.getJobName());
      case "PETROMIN" -> petrominService.getVehicleMaintenanceData(taskInfo, jobConfig);
      case "SAFEROAD" -> saferoadService.getVehicleTrackingInfo();
      default -> Collections.emptyList();
    };
  }

  private ReplicationJobConfiguration getJobConfigurationByName(String jobName) {
    if (Strings.isBlank(jobName)) {
      throw new BusinessException(BusinessErrors.INVALID_JOB_CONFIGURATION);
    }
    ReplicationJobConfiguration jobConfigInRedis = null; // fetchJobConfigFromRedis(jobName);
    if (Objects.isNull(jobConfigInRedis)) {
      log.warn("Job Configuration not found in Redis for Job-name={} ", jobName);
      ReplicationJobConfiguration jobConfigInDB =
          replicationJobConfigurationRepository.findByJobName(jobName);
      if (jobConfigInDB == null) {
        throw new BusinessException(BusinessErrors.JOB_CONFIGURATION_NOT_FOUND);
      } else {
        log.info("Job Configuration found in DB for Job-name={} ", jobName);
        cacheService.put(CacheUtil.getKeyForReplicationJobConfig(jobName), jobConfigInDB);
        return jobConfigInDB;
      }
    } else return jobConfigInRedis;
  }

  private ReplicationJobConfiguration fetchJobConfigFromRedis(String jobName) {
    String jobConfigCacheKey = CacheUtil.getKeyForReplicationJobConfig(jobName);
    return cacheService.get(jobConfigCacheKey, ReplicationJobConfiguration.class);
  }

  private void saveTaskInfo(
      Long replicationJobId,
      Integer jobId,
      Integer taskId,
      Integer fetchedRecord,
      Integer updatedRecord,
      String jobStatus,
      String remark,
      LocalDateTime startTime,
      LocalDateTime endTime) {
    ReplicationTaskHistory replicationTaskHistory =
        replicationJobMapper.mapReplicationTaskHistory(
            replicationJobId,
            jobId,
            taskId,
            fetchedRecord,
            updatedRecord,
            jobStatus,
            remark,
            startTime,
            endTime);
    try {
      replicationTaskHistoryRepository.save(replicationTaskHistory);
    } catch (Exception ex) {
      log.error(
          "Error while saving ReplicationTaskInfo in database request : {}",
          replicationTaskHistory);
    }
  }

  private List<Map<String, Object>> getUpdatedRecords(
      List<Map<String, Object>> carProDataList, ReplicationJobConfiguration jobConfig) {
    return carProDataList.stream()
        .map(this::cleanData)
        .filter(carProData -> isUpdated(carProData, jobConfig))
        .toList();
  }

  private Map<String, Object> cleanData(Map<String, Object> record) {
    record.forEach(
        (key, value) -> {
          if (value != null) {
            if (value instanceof Double) {
              record.put(key, String.format("%.0f", value));
            } else {
              record.put(key, value.toString().trim());
            }
          }
        });
    return record;
  }

  private boolean isUpdated(Map<String, Object> data, ReplicationJobConfiguration jobConfig) {
    Integer newHashCode = data.hashCode();
    String cacheKey =
        CacheUtil.getKeyForCacheHash(
            "ReplicationTaskCache", jobConfig.getJobName(), generateDataKey(data, jobConfig));
    Integer oldHashCode = cacheService.get(cacheKey, Integer.class);
    if (Objects.equals(newHashCode, oldHashCode)) {
      return false;
    } else {
      cacheService.put(cacheKey, newHashCode);
      return true;
    }
  }

  private String generateDataKey(Map<String, Object> data, ReplicationJobConfiguration jobConfig) {
    if (isNotEmpty(jobConfig.getCacheKeys())) {
      return jobConfig.getCacheKeys().stream()
          .map(key -> data.get(key))
          .filter(Objects::nonNull)
          .map(Object::toString)
          .collect(Collectors.joining("-"));
    } else {
      return String.valueOf(data.hashCode());
    }
  }
}
