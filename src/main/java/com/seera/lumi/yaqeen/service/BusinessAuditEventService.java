package com.seera.lumi.yaqeen.service;

import com.seera.lumi.yaqeen.domain.BusinessAuditEvent;
import com.seera.lumi.yaqeen.dto.BusinessAuditLogInfo;
import com.seera.lumi.yaqeen.repository.BusinessAuditEventRepository;
import com.seera.lumi.yaqeen.util.CommonUtils;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessAuditEventService {

  private final BusinessAuditEventRepository businessAuditEventRepository;

  public void processBusinessAuditEvent(BusinessAuditLogInfo event) {
    try {
      BusinessAuditEvent auditEvent = new BusinessAuditEvent();

      // Set basic fields
      auditEvent.setAuditId(event.getAuditId());
      auditEvent.setUserId(event.getUserId());
      auditEvent.setAction(event.getAction());
      auditEvent.setResourceType(event.getResourceType());
      auditEvent.setResourceValue(event.getResourceValue());
      auditEvent.setDescription(event.getDescription());
      auditEvent.setMethodName(event.getMethodName());
      auditEvent.setClassName(event.getClassName());

      // Set dynamic fields
      auditEvent.setParameters(CommonUtils.getStrFromObj(event.getParameters()));
      auditEvent.setResult(CommonUtils.getStrFromObj(event.getResult()));

      // Set metadata fields
      auditEvent.setTraceId(event.getTraceId());
      auditEvent.setClientId(event.getClientId());
      auditEvent.setTimestamp(event.getTimestamp());
      auditEvent.setHttpMethod(event.getHttpMethod());
      auditEvent.setRequestUri(event.getRequestUri());
      auditEvent.setUserAgent(event.getUserAgent());
      auditEvent.setIpAddress(event.getIpAddress());
      auditEvent.setSuccess(event.getSuccess());
      auditEvent.setErrorMessage(event.getErrorMessage());
      auditEvent.setExecutionTimeMs(event.getExecutionTimeMs());

      // Save to database
      businessAuditEventRepository.saveAndFlush(auditEvent);
      log.info("Business audit event saved successfully: {}", auditEvent.getAuditId());

    } catch (Exception e) {
      log.error("Error processing business audit event: {}", event, e);
    }
  }

  public Page<BusinessAuditEvent> getRecentBusinessAuditEvents(
      String userId,
      String action,
      String resourceType,
      String resourceValue,
      LocalDateTime startTime,
      LocalDateTime endTime,
      Boolean success,
      String clientId,
      int page,
      int pageSize) {

    Pageable pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.DESC, "timestamp"));
    return businessAuditEventRepository.findWithFilters(
        userId, action, resourceType, resourceValue, startTime, endTime, success, clientId, pageable);
  }

  public Page<BusinessAuditEvent> getBusinessAuditEventHistory(
      String userId,
      String action,
      String resourceType,
      String resourceValue,
      LocalDateTime startTime,
      LocalDateTime endTime,
      Boolean success,
      String clientId,
      int page,
      int pageSize) {

    Pageable pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.ASC, "timestamp"));
    return businessAuditEventRepository.findWithFilters(
        userId, action, resourceType, resourceValue, startTime, endTime, success, clientId, pageable);
  }

  public List<String> getUniqueUserIds(
          String action, String resourceType, String resourceValue, Boolean success, String clientId, LocalDateTime startTime, LocalDateTime endTime) {
    return businessAuditEventRepository.findUniqueUserIds(action, resourceType, resourceValue, success, clientId, startTime, endTime);
  }

  public List<String> getUniqueActions(
      String userId, String resourceType, String resourceValue, Boolean success, String clientId, LocalDateTime startTime, LocalDateTime endTime) {
    return businessAuditEventRepository.findUniqueActions(
        userId, resourceType, resourceValue, success, clientId, startTime, endTime);
  }

  public List<String> getUniqueResourceTypes(
      String userId,
      String action,
      String resourceValue,
      Boolean success,
      String clientId,
      LocalDateTime startTime,
      LocalDateTime endTime) {
    return businessAuditEventRepository.findUniqueResourceTypes(
        userId, action, resourceValue, success, clientId, startTime, endTime);
  }

  public List<String> getUniqueResourceValues(
      String userId,
      String action,
      String resourceType,
      Boolean success,
      String clientId,
      LocalDateTime startTime,
      LocalDateTime endTime) {
    return businessAuditEventRepository.findUniqueResourceValues(
        userId, action, resourceType, success, clientId, startTime, endTime);
  }

  public BusinessAuditEvent findByAuditId(String auditId) {
    return businessAuditEventRepository.findByAuditId(auditId);
  }

  public List<BusinessAuditEvent> findByTraceId(String traceId) {
    return businessAuditEventRepository.findByTraceId(traceId);
  }

  public List<String> getUniqueClientIds(String userId, String action, String resourceType, String resourceValue, Boolean success, LocalDateTime startTime,
        LocalDateTime endTime) {
    return businessAuditEventRepository.findUniqueClientIds(userId, action, resourceType, resourceValue, success, startTime, endTime);
  }
}
