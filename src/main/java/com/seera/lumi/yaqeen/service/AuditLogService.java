package com.seera.lumi.yaqeen.service;

import com.seera.lumi.yaqeen.ai.response.BaseResponse;
import com.seera.lumi.yaqeen.ai.selector.AIModelSelector;
import com.seera.lumi.yaqeen.ai.service.AIService;
import com.seera.lumi.yaqeen.domain.AuditEvent;
import com.seera.lumi.yaqeen.dto.AuditLogInfo;
import com.seera.lumi.yaqeen.repository.AuditEventRepository;
import com.seera.lumi.yaqeen.util.CommonUtils;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuditLogService {

  private final AuditEventRepository auditEventRepository;
  private final AIService aiService;
  private final AIModelSelector modelSelector;

  @Value("${audit.skip-tables:}")
  private String skipTables;

  public void processAuditLog(AuditLogInfo event) {
    AuditEvent audit = new AuditEvent();
    String pkValue = null;

    if (shouldSkipAudit(event)) {
      return;
    }

    if (event.getOp().equals("c") || event.getOp().equals("r") || event.getOp().equals("u")) {
      pkValue = getPrimaryKeyValue(event.getAfter(), event);
      if (event.getAfter().containsKey("updated_by")) {
        audit.setUpdatedBy(String.valueOf(event.getAfter().get("updated_by")));
      } else {
        audit.setUpdatedBy("N/A");
      }
    } else if (event.getOp().equals("d")) {
      pkValue = getPrimaryKeyValue(event.getBefore(), event);
      if (event.getBefore().containsKey("updated_by")) {
        audit.setUpdatedBy(String.valueOf(event.getBefore().get("updated_by")));
      } else {
        audit.setUpdatedBy("N/A");
      }
    }

    audit.setDatabase(event.getSource().getDb());
    audit.setTable(event.getSource().getTable());
    audit.setPrimaryKey(pkValue);
    audit.setOperation(getOperation(event.getOp()));
    // audit.setSnapsort(event.getAfter().toString());
    audit.setSnapshot(CommonUtils.getStrFromObj(event.getAfter()));
    audit.setTimestamp(event.getTs_ms());

    auditEventRepository.saveAndFlush(audit);
    log.info("Audit event saved : {}", audit.getDatabase() + "." + audit.getTable());
  }

  private boolean shouldSkipAudit(AuditLogInfo event) {
    String database = event.getSource().getDb();
    String table = event.getSource().getTable();

    // Skip specific tables regardless of database
    List<String> globalSkipTables =
        Arrays.asList(
            "DATABASECHANGELOG",
            "DATABASECHANGELOGLOCK",
            "multilingual",
            "revinfo",
            "revinfo_seq",
            "flyway_schema_history",
            "user-backup",
            "id_generator");

    // Skip database-specific tables
    List<String> databaseSpecificSkipTables =
        Arrays.asList(
            "booking_service.booking_metadata",
            "booking_service.cancellation_receipts",
            "branch_service.branch_tags",
            "lumi-core-agreement-service.addon_type_mapping");

    // Check if table should be skipped globally
    if (globalSkipTables.contains(table)) {
      log.debug("Skipping audit for global skip table: {}", table);
      return true;
    }

    // Check if table ends with "aud"
    if (table.endsWith("aud")) {
      log.debug("Skipping audit for table ending with 'aud': {}", table);
      return true;
    }

    // Check database-specific skip tables
    String databaseTableKey = database + "." + table;
    if (databaseSpecificSkipTables.contains(databaseTableKey)) {
      log.debug("Skipping audit for database-specific table: {}", databaseTableKey);
      return true;
    }

    // Check property file skip tables
    if (!skipTables.isEmpty()) {
      List<String> propertySkipTables = Arrays.asList(skipTables.split(","));
      if (propertySkipTables.contains(table)) {
        log.debug("Skipping audit for property file table: {}", table);
        return true;
      }
    }

    return false;
  }

  public Page<AuditEvent> getRecentAuditEventsWithFilters(
      String databaseName,
      String tableName,
      String key,
      String user,
      Long startTime,
      Long endTime,
      int page,
      int pageSize) {
    return auditEventRepository.findRecentAuditEventsWithFilters(
        databaseName, tableName, key, user, startTime, endTime, PageRequest.of(page, pageSize));
  }

  public Page<AuditEvent> getHistoryAuditEventsWithFilters(
      String databaseName,
      String tableName,
      String key,
      String user,
      Long startTime,
      Long endTime,
      int page,
      int pageSize) {
    return auditEventRepository.findHistoryAuditEventsWithFilters(
        databaseName, tableName, key, user, startTime, endTime, PageRequest.of(page, pageSize));
  }

  public List<String> getUniqueAuditDatabases(Long startTime, Long endTime) {
    return auditEventRepository.findUniqueDatabasesByTimeRange(startTime, endTime);
  }

  public List<String> getUniqueAuditTables(String databaseName, Long startTime, Long endTime) {
    return auditEventRepository.findUniqueTablesByDatabaseAndTimeRange(
        databaseName, startTime, endTime);
  }

  public List<String> getUniqueAuditKeys(
      String databaseName, String tableName, Long startTime, Long endTime) {
    return auditEventRepository.findUniqueKeysByDatabaseAndTableAndTimeRange(
        databaseName, tableName, startTime, endTime);
  }

  public List<String> getUniqueAuditUsers(
      String databaseName, String tableName, Long startTime, Long endTime) {
    return auditEventRepository.findUniqueUsersByDatabaseAndTimeRange(
        databaseName, tableName, startTime, endTime);
  }

  public List<AuditEvent> getAuditEventsWithBlankRemarks() {
    return auditEventRepository
        .findAuditEventsWithBlankRemarks(PageRequest.of(0, 100000))
        .getContent();
  }

  @Async
  public void processAuditEventsWithBlankRemarks(List<AuditEvent> auditEvents) {

    // log.info("Found {} audit events with blank remarks", auditEvents.size());

    for (AuditEvent auditEvent : auditEvents) {
      try {
        if (auditEvent.getOperation().equals("snapshot")
            || auditEvent.getOperation().equals("create")) {
          auditEvent.setRemarks("Audit snapshot");
        } else if (auditEvent.getOperation().equals("delete")) {
          auditEvent.setRemarks("Record deleted");
        } else if (auditEvent.getOperation().equals("update")) {
          AuditEvent lastSnapshot =
              auditEventRepository
                  .findSecondLastSnapshot(
                      auditEvent.getDatabase(),
                      auditEvent.getTable(),
                      auditEvent.getPrimaryKey(),
                      auditEvent.getTimestamp(),
                      PageRequest.of(0, 1))
                  .getContent()
                  .get(0);
          if (lastSnapshot != null) {
            auditEvent.setRemarks(generateDiffText(auditEvent, lastSnapshot));
          }
        }
        auditEventRepository.saveAndFlush(auditEvent);
        log.info(
            "Audit Remarks generated: {}", auditEvent.getDatabase() + "." + auditEvent.getTable());
      } catch (Exception e) {
        log.error("Error processing audit event ID: {}", auditEvent.getId(), e);
      }
    }
  }

  private String generateDiffText(AuditEvent auditEvent, AuditEvent lastSnapshot) {
    // log.info("Generating diff text for audit event: {}", auditEvent.toString());
    // log.info("Last snapshot: {}", lastSnapshot.toString());
    String prompt =
        String.format(
            """
            You are an assistant that compares two database row snapshots and generates a one-sentence summary of what changed.

            Instructions:
            - Only mention the fields whose values have changed.
            - Ignore technical fields like `updated_on` and `created_on`.
            - Include the user who made the change, the database name, the table name, and the primary key.
            - Format the output like:
              <User> updated <Table> Id <Primary Key> by changing field1 from oldValue1 to newValue1, field2 from oldValue2 to newValue2, ...

            Metadata:
            User: %s \s
            Database: %s \s
            Table: %s \s
            Primary Key: %s \s

            Before
            %s

            After
            %s
            """,
            auditEvent.getUpdatedBy(),
            auditEvent.getDatabase(),
            auditEvent.getTable(),
            auditEvent.getPrimaryKey(),
            lastSnapshot.getSnapshot(),
            auditEvent.getSnapshot());
    BaseResponse response = aiService.generateContent(prompt, modelSelector.selectBestModel());
    // log.info("Remarks generated: {}", response.getMessage());
    return response.getMessage();
  }

  private String getOperation(String op) {
    if (op.equals("c")) {
      return "create";
    } else if (op.equals("r")) {
      return "snapshot";
    } else if (op.equals("u")) {
      return "update";
    } else if (op.equals("d")) {
      return "delete";
    }
    return "unknown";
  }

  private String getPrimaryKeyValue(Map<String, Object> map, AuditLogInfo event) {
    if (map.containsKey("id")) {
      return map.get("id").toString();
    } else {
      for (String key : map.keySet()) {
        log.info(
            "Id Column not found, using key: {} for table: {}", key, event.getSource().getTable());
        return map.get(key).toString();
      }
    }
    return null;
  }
}
