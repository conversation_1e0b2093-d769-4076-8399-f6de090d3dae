package com.seera.lumi.yaqeen.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmailService {

    private final JavaMailSender mailSender;
    
    @Value("${spring.profiles.active:local}")
    private String activeProfile;

    public void sendCsvFileEmail(String to, String cc, String bcc, String subject, String body, Map<String, java.io.File> reports) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            if (to != null && !to.trim().isEmpty()) {
                helper.setTo(to.split(","));
            }
            if (cc != null && !cc.trim().isEmpty()) {
                helper.setCc(cc.split(","));
            }
            if (bcc != null && !bcc.trim().isEmpty()) {
                helper.setBcc(bcc.split(","));
            }
            helper.setSubject(subject);
            helper.setText(body, false); // false for plain text

            // Attach the CSV files
            for (Map.Entry<String, java.io.File> entry : reports.entrySet()) {
                FileSystemResource file = new FileSystemResource(entry.getValue());
                helper.addAttachment(entry.getKey(), file);
            }

            mailSender.send(message);
            log.info("Email sent successfully to: {} with CSV attachments: {}", to, reports.keySet());

        } catch (MessagingException e) {
            log.error("Failed to send email to: {} with attachment: {}", to, reports.keySet(), e);
            throw new RuntimeException("Failed to send email", e);
        }
    }

    public void sendFinanceReportsEmail(Map<String, java.io.File> reports) {
        // Environment-based email configuration
        String to;
        String cc;
        String bcc;
        if ("prod".equalsIgnoreCase(activeProfile)) {
            // Production environment - send to business email
            to = "<EMAIL>";
            cc = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>";
            bcc = "<EMAIL>,<EMAIL>";
        } else {
            // Non-production environment - send to developer email
            to = "<EMAIL>";
            cc = "<EMAIL>,<EMAIL>";
            bcc = "<EMAIL>,<EMAIL>,<EMAIL>";
        }
        
        String today = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("dd MMMM yyyy"));
        String subject = today + " - Yaqeen Reports";
        String body = "Please find the attached reports for All open agreements, Invoices, All Agreements, Daily Cash Report and Register list by branch Report\n\n" +
                     "This is an automated report generated by the Lumi Core system. Please reach out to the Lumi Digital team if you have any concerns.\n";

        sendCsvFileEmail(to, cc, bcc, subject, body, reports);
    }

    public void sendTrafficFineEmail(Map<String, java.io.File> reports) {
        // Environment-based email configuration
        String to;
        String cc;
        String bcc = null;
        if ("prod".equalsIgnoreCase(activeProfile)) {
            // Production environment - send to business email
            to = "<EMAIL>,<EMAIL>,<EMAIL>";
            cc = "<EMAIL>,<EMAIL>";
        } else {
            // Non-production environment - send to developer email
            to = "<EMAIL>";
            cc = "<EMAIL>";
            bcc = "<EMAIL>";
        }
        
        String today = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("dd MMMM yyyy"));
        String subject = today + " - Traffic Fine Reports";
        String body = "Please find the attached reports for Traffic Fine Reports\n\n" +
                     "This is an automated report generated by the Lumi Core system. Please reach out to the Lumi Digital team if you have any concerns.\n";

        sendCsvFileEmail(to, cc, bcc, subject, body, reports);
    }
} 
