package com.seera.lumi.yaqeen.service;

import com.seera.lumi.yaqeen.controller.request.CreateLeaseCustomerRequest;
import com.seera.lumi.yaqeen.controller.request.CustomerAccountSearchRequestDTO;
import com.seera.lumi.yaqeen.controller.request.SyncCarProCustomerData;
import com.seera.lumi.yaqeen.controller.request.UpdateLeaseCustomerRequest;
import com.seera.lumi.yaqeen.controller.resp.CustomerAccountBasicResponse;
import com.seera.lumi.yaqeen.controller.resp.CustomerAccountResponse;
import com.seera.lumi.yaqeen.domain.CustomerAccount;
import com.seera.lumi.yaqeen.exception.BusinessException;
import java.util.List;
import org.springframework.data.domain.Page;

public interface CustomerAccountService {

  Page<CustomerAccountBasicResponse> findAll(CustomerAccountSearchRequestDTO searchRequest);

  CustomerAccountResponse findById(Long customerAccountId);

  CustomerAccountResponse findBySapId(String sapId);

  CustomerAccountResponse findByCarProId(String carProId);

  CustomerAccountResponse createCustomerAccount(CreateLeaseCustomerRequest request);

  CustomerAccount updateCustomerAccount(UpdateLeaseCustomerRequest request);

  void deactivateCustomerAccount(Long id);

  List<String> getAllCustomerAccountTypes();

  List<String> getAllCustomerAccountRegions();

  void syncCarProCustomer(SyncCarProCustomerData event) throws BusinessException;
}
