package com.seera.lumi.yaqeen.service;

import static java.util.Objects.nonNull;

import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultMessagePublisher<T> {

  private final KafkaTemplate<Object, Object> defaultKafkaTemplate;

  public CompletableFuture publishMessage(T message, String topic, String key, String entityType) {
    ProducerRecord producerRecord = new ProducerRecord(topic, null, key, message);
    CompletableFuture completableFuture = defaultKafkaTemplate.send(producerRecord);
    completableFuture.whenComplete(
        (result, ex) -> {
          if (nonNull(ex)) {
            log.error(
                "Kafka Event failed:{} Topic {} | partitionKey {} | partitionValue {} | message {} ",
                ex,
                topic,
                key,
                entityType,
                message);
          }
        });
    log.info("Published message to topic={} key={} ", topic, key);
    return completableFuture;
  }
}
