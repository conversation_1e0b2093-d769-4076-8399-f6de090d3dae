package com.seera.lumi.yaqeen.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.seera.lumi.yaqeen.api.petromin.PetrominClient;
import com.seera.lumi.yaqeen.api.petromin.request.PetrominDeliveryNoteRequest;
import com.seera.lumi.yaqeen.controller.request.ReplicationTaskInfo;
import com.seera.lumi.yaqeen.domain.ReplicationJobConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PetrominService {

  @Value("${api.petromin.base.url}")
  private String baseUrl;

  @Autowired private PetrominClient petrominClient;

  public List<Map<String, Object>> getVehicleMaintenanceData(
      ReplicationTaskInfo taskInfo, ReplicationJobConfiguration jobConfig)
      throws JsonProcessingException {
    var request =
        PetrominDeliveryNoteRequest.builder()
            .page(taskInfo.getTaskId())
            .limit(jobConfig.getBatchSize())
            .from(taskInfo.getFrom())
            .to(taskInfo.getTo())
            .build();

    var url = URI.create(baseUrl + "deliverynotes/get");
    var petrominResponse = petrominClient.getData(url, request);

    return switch (petrominResponse.getData()) {
      case LinkedHashMap<?, ?> dataMap -> (List<Map<String, Object>>) dataMap.get("data");
      default ->
          throw new IllegalArgumentException(
              "Unexpected data format: " + petrominResponse.getData().getClass());
    };
  }
}
