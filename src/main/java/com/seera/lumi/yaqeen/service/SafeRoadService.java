package com.seera.lumi.yaqeen.service;

import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.ListUtils.partition;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.seera.lumi.yaqeen.api.saferoad.SafeRoadClient;
import com.seera.lumi.yaqeen.api.saferoad.request.SafeRoadLoginRequest;
import com.seera.lumi.yaqeen.api.saferoad.response.SafeRoadLoginResponse;
import feign.Response;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SafeRoadService {

  private static final String BEARER_HEADER_PREFIX = "Bearer ";

  @Value("${api.saferoad.username}")
  private String username;

  @Value("${api.saferoad.password}")
  private String password;

  @Autowired private ObjectMapper objectMapper;

  @Autowired private SafeRoadClient saferoadClient;

  public List<Map<String, Object>> getVehicleTrackingInfo() {
    try {
      String safeRoadAuthToken = getSafeRoadAuthToken();
      String bearerToken = BEARER_HEADER_PREFIX + safeRoadAuthToken;
      Response saferoadHttpResponse = saferoadClient.getVehicleSettings(bearerToken);
      List<Map<String, Object>> saferoadResponse =
          objectMapper.readValue(
              saferoadHttpResponse.body().asInputStream(), new TypeReference<>() {});
      Map<String, Map<String, Object>> responseByPlateNumber =
          getAttributesByPlateNo(saferoadResponse);
      List<String> neomFleet = getNeomPlate(saferoadResponse);
      overwriteByShareXResponse(responseByPlateNumber, neomFleet, bearerToken);
      return new ArrayList<>(responseByPlateNumber.values());
    } catch (Exception e) {
      log.error("Error while calling SafeRoad Api for vehicle tracking info " + e);
      throw new RuntimeException(e);
    }
  }

  private void overwriteByShareXResponse(
      Map<String, Map<String, Object>> responseByPlateNumber,
      List<String> neomFleet,
      String bearerToken) {
    partition(neomFleet, 50)
        .forEach(
            plates -> {
              Map<String, Map<String, Object>> shareXResponse = callShareX(plates, bearerToken);
              if (shareXResponse != null) {
                updateResult(shareXResponse, responseByPlateNumber);
              }
            });
  }

  private Map<String, Map<String, Object>> callShareX(List<String> plates, String bearerToken) {
    try {
      log.info("Calling ShareX api for plates {}", plates);
      Response shareXHttpResponse =
          saferoadClient.getVehicleMileage(String.join(",", plates), bearerToken);

      // Read and parse response body
      InputStream responseBody = shareXHttpResponse.body().asInputStream();
      String responseBodyString = new String(responseBody.readAllBytes(), StandardCharsets.UTF_8);
      Map<String, Object> responseMap =
          objectMapper.readValue(responseBodyString, new TypeReference<Map<String, Object>>() {});

      // Extract "vehicles" and filter
      List<Map<String, Object>> shareXResponse =
          (List<Map<String, Object>>) responseMap.get("vehicles");
      if (shareXResponse != null) {
        return getAttributesByPlateNo(
            shareXResponse.stream()
                .filter(x -> x.get("Mileage") != null && x.get("RecordDateTime") != null)
                .collect(toList()));
      }
    } catch (Exception e) {
      log.error("Error while calling ShareX API for plates: {}. Error: {}", plates, e.getMessage());
    }
    return Collections.emptyMap(); // Return an empty map in case of error
  }

  private void updateResult(
      Map<String, Map<String, Object>> shareXResponse,
      Map<String, Map<String, Object>> finalResponse) {
    if (shareXResponse != null && !shareXResponse.isEmpty()) {
      for (Map<String, Object> shareXResp : shareXResponse.values()) {
        String plateNo = shareXResp.get("DisplayName").toString();
        finalResponse.get(plateNo.toUpperCase()).put("Mileage", shareXResp.get("Mileage"));
      }
    }
  }

  private List<String> getNeomPlate(List<Map<String, Object>> saferoadResponse) {
    return saferoadResponse.stream()
        .filter(response -> "NEOM Lease".equals(response.get("GroupName")))
        .map(response -> ((String) response.get("DisplayName")).toUpperCase())
        .collect(Collectors.toList());
  }

  private Map<String, Map<String, Object>> getAttributesByPlateNo(
      List<Map<String, Object>> responseList) {
    Map<String, Map<String, Object>> result = new HashMap<>();
    for (Map<String, Object> response : responseList) {
      if (response.get("DisplayName") != null) {
        String plateNo = (String) response.get("DisplayName");
        result.put(plateNo.toUpperCase(), response);
      }
    }
    return result;
  }

  private String getSafeRoadAuthToken() {
    SafeRoadLoginRequest loginRequest = new SafeRoadLoginRequest(username, password);
    SafeRoadLoginResponse loginResponse = saferoadClient.getSafeRoadToken(loginRequest);
    return loginResponse.getNewToken();
  }
}
