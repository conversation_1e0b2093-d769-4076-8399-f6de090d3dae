package com.seera.lumi.yaqeen.service;

import com.seera.lumi.yaqeen.api.carpro.CarProClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CarProService {

  @Autowired private CarProClient carProClient;

  public List<Map<String, Object>> getData(Integer taskId, String jobName) {
    ResponseEntity<Object> response = carProClient.getAllData(taskId, Boolean.FALSE, jobName);
    return (List<Map<String, Object>>) response.getBody();
  }
}
