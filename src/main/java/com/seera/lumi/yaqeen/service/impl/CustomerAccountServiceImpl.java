package com.seera.lumi.yaqeen.service.impl;

import static com.seera.lumi.yaqeen.util.PageUtil.getPageable;
import static java.util.Objects.isNull;
import static java.util.stream.Collectors.toUnmodifiableList;

import com.seera.lumi.yaqeen.controller.request.CreateLeaseCustomerRequest;
import com.seera.lumi.yaqeen.controller.request.CustomerAccountSearchRequestDTO;
import com.seera.lumi.yaqeen.controller.request.SyncCarProCustomerData;
import com.seera.lumi.yaqeen.controller.request.UpdateLeaseCustomerRequest;
import com.seera.lumi.yaqeen.controller.resp.CustomerAccountBasicResponse;
import com.seera.lumi.yaqeen.controller.resp.CustomerAccountResponse;
import com.seera.lumi.yaqeen.domain.Customer;
import com.seera.lumi.yaqeen.domain.CustomerAccount;
import com.seera.lumi.yaqeen.exception.BusinessErrors;
import com.seera.lumi.yaqeen.exception.BusinessException;
import com.seera.lumi.yaqeen.mapper.LeaseCustomerMapper;
import com.seera.lumi.yaqeen.repository.CustomerAccountRepository;
import com.seera.lumi.yaqeen.repository.specification.CustomerAccountSpecification;
import com.seera.lumi.yaqeen.service.CustomerAccountService;
import com.seera.lumi.yaqeen.service.CustomerService;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerAccountServiceImpl implements CustomerAccountService {

  private final CustomerService customerService;
  private final CustomerAccountRepository customerAccountRepository;
  private final LeaseCustomerMapper customerMapper;

  @Override
  public Page<CustomerAccountBasicResponse> findAll(CustomerAccountSearchRequestDTO searchRequest) {
    return customerAccountRepository
        .findAll(new CustomerAccountSpecification(searchRequest), getPageable(searchRequest))
        .map(account -> customerMapper.toBasicResponse(account));
  }

  @Override
  public CustomerAccountResponse findById(Long customerAccountId) {
    CustomerAccount customerAccount =
        customerAccountRepository
            .findById(customerAccountId)
            .orElseThrow(() -> new BusinessException(BusinessErrors.CUSTOMER_ACCOUNT_NOT_FOUND));
    return customerMapper.toResponse(customerAccount);
  }

  @Override
  public CustomerAccountResponse findBySapId(String sapId) {
    CustomerAccount customerAccount =
        customerAccountRepository
            .findBySapId(String.valueOf(sapId))
            .orElseThrow(() -> new BusinessException(BusinessErrors.CUSTOMER_ACCOUNT_NOT_FOUND));
    return customerMapper.toResponse(customerAccount);
  }

  @Override
  public CustomerAccountResponse findByCarProId(String carProId) {
    CustomerAccount customerAccount =
        customerAccountRepository
            .findByCarProId(carProId)
            .orElseThrow(() -> new BusinessException(BusinessErrors.CUSTOMER_ACCOUNT_NOT_FOUND));
    return customerMapper.toResponse(customerAccount);
  }

  @Override
  public CustomerAccountResponse createCustomerAccount(final CreateLeaseCustomerRequest request) {
    Customer customer =
        customerService
            .findByVatNo(Long.valueOf(request.getVatNo()))
            .orElse(customerMapper.toCustomer(request));
    CustomerAccount customerAccount = customerMapper.buildCustomerAccount(request);
    customerAccount.setIsActive(Boolean.TRUE);
    customerAccount.setCreatedOn(LocalDateTime.now());
    customerAccount.setCustomer(customer);
    customer.getCustomerAccounts().add(customerAccount);
    customerService.save(customer);
    return customerMapper.toResponse(customerAccount);
  }

  @Override
  public CustomerAccount updateCustomerAccount(UpdateLeaseCustomerRequest request) {
    CustomerAccount customerAccount =
        customerAccountRepository
            .findById(request.getId())
            .orElseThrow(() -> new BusinessException(BusinessErrors.CUSTOMER_ACCOUNT_NOT_FOUND));
    customerMapper.updateCustomerAccountFromBaseLeaseRequest(request, customerAccount);
    return customerAccount;
  }

  @Override
  public void deactivateCustomerAccount(Long id) {
    CustomerAccount customerAccount =
        customerAccountRepository
            .findById(id)
            .orElseThrow(() -> new BusinessException(BusinessErrors.CUSTOMER_ACCOUNT_NOT_FOUND));
    customerAccount.setIsActive(Boolean.FALSE);
    //    customerAccount.setTerminatedOn(new Date());
    customerAccountRepository.save(customerAccount);
  }

  @Override
  public List<String> getAllCustomerAccountTypes() {
    return customerAccountRepository.findDistinctAccountTypes().stream()
        .filter(ObjectUtils::isNotEmpty)
        .collect(toUnmodifiableList());
  }

  @Override
  public List<String> getAllCustomerAccountRegions() {
    return customerAccountRepository.findDistinctAccountRegions().stream()
        .filter(ObjectUtils::isNotEmpty)
        .collect(toUnmodifiableList());
  }

  @Override
  @Transactional
  public void syncCarProCustomer(SyncCarProCustomerData customerData) throws BusinessException {
    if (isInvalidVatNo(customerData)) {
      handleInvalidTaxOfficeNo(customerData);
    } else {
      Customer customer =
          customerService
              .findByVatNo(customerMapper.mapVatNo(customerData))
              .orElse(customerMapper.toCustomer(customerData));
      CustomerAccount customerAccount =
          customer.getCustomerAccounts().stream()
              .filter(account -> account.getCarProId().equals(customerData.getCarProId()))
              .findFirst()
              .map(
                  account -> {
                    customerMapper.updateCustomerAccount(customerData, account);
                    return account;
                  })
              .orElse(customerMapper.toCustomerAccount(customerData));
      customerAccount.setCustomer(customer);
      customer.getCustomerAccounts().add(customerAccount);
      customerService.save(customer);
    }
  }

  private boolean isInvalidVatNo(SyncCarProCustomerData customerData) {
    Long vatNo = customerMapper.mapVatNo(customerData);
    return isNull(vatNo) || vatNo.equals(0);
  }

  private void handleInvalidTaxOfficeNo(SyncCarProCustomerData customerData) {
    log.error("Tax Office No is not available for customer account, request: {}", customerData);
    CustomerAccount customerAccount =
        customerAccountRepository
            .findByCarProId(customerData.getCarProId())
            .map(
                account -> {
                  customerMapper.updateCustomerAccount(customerData, account);
                  return account;
                })
            .orElse(customerMapper.toCustomerAccount(customerData));
    customerAccountRepository.save(customerAccount);
  }
}
