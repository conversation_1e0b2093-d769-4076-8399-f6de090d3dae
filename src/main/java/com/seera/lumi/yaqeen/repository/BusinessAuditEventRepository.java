package com.seera.lumi.yaqeen.repository;

import com.seera.lumi.yaqeen.domain.BusinessAuditEvent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface BusinessAuditEventRepository extends JpaRepository<BusinessAuditEvent, Long> {

  @Query(
      "SELECT bae FROM BusinessAuditEvent bae WHERE "
          + "(:userId IS NULL OR bae.userId = :userId) AND "
          + "(:action IS NULL OR bae.action = :action) AND "
          + "(:resourceType IS NULL OR bae.resourceType = :resourceType) AND "
          + "(:resourceValue IS NULL OR bae.resourceValue like :resourceValue%) AND "
          + "(:startTime IS NULL OR bae.timestamp >= :startTime) AND "
          + "(:endTime IS NULL OR bae.timestamp <= :endTime) AND "
          + "(:success IS NULL OR bae.success = :success) AND "
          + "(:clientId IS NULL OR bae.clientId = :clientId)")
  Page<BusinessAuditEvent> findWithFilters(
      @Param("userId") String userId,
      @Param("action") String action,
      @Param("resourceType") String resourceType,
      @Param("resourceValue") String resourceValue,
      @Param("startTime") LocalDateTime startTime,
      @Param("endTime") LocalDateTime endTime,
      @Param("success") Boolean success,
      @Param("clientId") String clientId,
      Pageable pageable);

  @Query(
      "SELECT DISTINCT bae.userId FROM BusinessAuditEvent bae WHERE "
          + "bae.userId IS NOT NULL AND "
          + "(:action IS NULL OR bae.action = :action) AND "
          + "(:resourceType IS NULL OR bae.resourceType = :resourceType) AND "
          + "(:resourceValue IS NULL OR bae.resourceValue LIKE CONCAT('%', :resourceValue, '%')) AND "
          + "(:success IS NULL OR bae.success = :success) AND "
          + "(:clientId IS NULL OR bae.clientId = :clientId) AND "
          + "(:startTime IS NULL OR bae.timestamp >= :startTime) AND "
          + "(:endTime IS NULL OR bae.timestamp <= :endTime)")
  List<String> findUniqueUserIds(
      @Param("action") String action,
      @Param("resourceType") String resourceType,
      @Param("resourceValue") String resourceValue,
      @Param("success") Boolean success,
      @Param("clientId") String clientId,
      @Param("startTime") LocalDateTime startTime,
      @Param("endTime") LocalDateTime endTime);

  @Query(
      "SELECT DISTINCT bae.action FROM BusinessAuditEvent bae WHERE "
          + "bae.action IS NOT NULL AND "
          + "(:userId IS NULL OR bae.userId = :userId) AND "
          + "(:resourceType IS NULL OR bae.resourceType = :resourceType) AND "
          + "(:success IS NULL OR bae.success = :success) AND "
          + "(:clientId IS NULL OR bae.clientId = :clientId) AND "
          + "(:resourceValue IS NULL OR bae.resourceValue LIKE CONCAT('%', :resourceValue, '%')) AND "
          + "(:startTime IS NULL OR bae.timestamp >= :startTime) AND "
          + "(:endTime IS NULL OR bae.timestamp <= :endTime)")
  List<String> findUniqueActions(
      @Param("userId") String userId,
      @Param("resourceType") String resourceType,
      @Param("resourceValue") String resourceValue,
      @Param("success") Boolean success,
      @Param("clientId") String clientId,   
      @Param("startTime") LocalDateTime startTime,
      @Param("endTime") LocalDateTime endTime);

  @Query(
      "SELECT DISTINCT bae.resourceType FROM BusinessAuditEvent bae WHERE "
          + "bae.resourceType IS NOT NULL AND "
          + "(:userId IS NULL OR bae.userId = :userId) AND "
          + "(:action IS NULL OR bae.action = :action) AND "
          + "(:resourceValue IS NULL OR bae.resourceValue LIKE CONCAT('%', :resourceValue, '%')) AND "
          + "(:clientId IS NULL OR bae.clientId = :clientId) AND "
          + "(:success IS NULL OR bae.success = :success) AND "
          + "(:startTime IS NULL OR bae.timestamp >= :startTime) AND "
          + "(:endTime IS NULL OR bae.timestamp <= :endTime)")
  List<String> findUniqueResourceTypes(
      @Param("userId") String userId,
      @Param("action") String action,
      @Param("resourceValue") String resourceValue,
      @Param("success") Boolean success,
      @Param("clientId") String clientId,
      @Param("startTime") LocalDateTime startTime,
      @Param("endTime") LocalDateTime endTime);

  @Query(
      "SELECT DISTINCT bae.resourceValue FROM BusinessAuditEvent bae WHERE "
          + "bae.resourceValue IS NOT NULL AND "
          + "(:userId IS NULL OR bae.userId = :userId) AND "
          + "(:action IS NULL OR bae.action = :action) AND "
          + "(:resourceType IS NULL OR bae.resourceType = :resourceType) AND "
          + "(:success IS NULL OR bae.success = :success) AND "
          + "(:clientId IS NULL OR bae.clientId = :clientId) AND "
          + "(:startTime IS NULL OR bae.timestamp >= :startTime) AND "
          + "(:endTime IS NULL OR bae.timestamp <= :endTime)")
  List<String> findUniqueResourceValues(
      @Param("userId") String userId,
      @Param("action") String action,
      @Param("resourceType") String resourceType,
      @Param("success") Boolean success,
      @Param("clientId") String clientId,
      @Param("startTime") LocalDateTime startTime,
      @Param("endTime") LocalDateTime endTime);

  @Query("SELECT bae FROM BusinessAuditEvent bae WHERE bae.auditId = :auditId")
  BusinessAuditEvent findByAuditId(@Param("auditId") String auditId);

  @Query("SELECT bae FROM BusinessAuditEvent bae WHERE bae.traceId = :traceId")
  List<BusinessAuditEvent> findByTraceId(@Param("traceId") String traceId);

  @Query(
      "SELECT DISTINCT bae.clientId FROM BusinessAuditEvent bae WHERE "
          + "bae.clientId IS NOT NULL AND "
          + "(:userId IS NULL OR bae.userId = :userId) AND "
          + "(:action IS NULL OR bae.action = :action) AND "
          + "(:resourceType IS NULL OR bae.resourceType = :resourceType) AND "
          + "(:resourceValue IS NULL OR bae.resourceValue = :resourceValue) AND "
          + "(:success IS NULL OR bae.success = :success) AND "
          + "(:startTime IS NULL OR bae.timestamp >= :startTime) AND "
          + "(:endTime IS NULL OR bae.timestamp <= :endTime)")
  List<String> findUniqueClientIds(
      @Param("userId") String userId,
      @Param("action") String action,
      @Param("resourceType") String resourceType,
      @Param("resourceValue") String resourceValue,
      @Param("success") Boolean success,
      @Param("startTime") LocalDateTime startTime,
      @Param("endTime") LocalDateTime endTime);
}
