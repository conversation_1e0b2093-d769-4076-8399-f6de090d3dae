package com.seera.lumi.yaqeen.repository;

import com.seera.lumi.yaqeen.domain.AuditEvent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AuditEventRepository extends JpaRepository<AuditEvent, Long> {

  /** Find recent audit events with filters and pagination */
  @Query(
      """
            SELECT a FROM AuditEvent a WHERE
            (:databaseName IS NULL OR a.database = :databaseName) AND
            (:tableName IS NULL OR a.table = :tableName) AND
            (:key IS NULL OR a.primaryKey = :key) AND
            (:user IS NULL OR a.updatedBy = :user) AND
            (:startTime IS NULL OR a.timestamp >= :startTime) AND
            (:endTime IS NULL OR a.timestamp <= :endTime) AND
            a.table NOT IN ('DATABASECHANGELOG', 'DATABASECHANGELOGLOCK')
            ORDER BY a.timestamp DESC
            """)
  Page<AuditEvent> findRecentAuditEventsWithFilters(
      @Param("databaseName") String databaseName,
      @Param("tableName") String tableName,
      @Param("key") String key,
      @Param("user") String user,
      @Param("startTime") Long startTime,
      @Param("endTime") Long endTime,
      Pageable pageable);

  /** Find unique database names within time range */
  @Query(
      """
            SELECT DISTINCT a.database FROM AuditEvent a WHERE
            (:startTime IS NULL OR a.timestamp >= :startTime) AND
            (:endTime IS NULL OR a.timestamp <= :endTime)
            """)
  List<String> findUniqueDatabasesByTimeRange(
      @Param("startTime") Long startTime, @Param("endTime") Long endTime);

  /** Find unique tables by database and time range */
  @Query(
      """
                SELECT DISTINCT a.table FROM AuditEvent a WHERE
                (:database IS NULL OR a.database = :database) AND
                (:startTime IS NULL OR a.timestamp >= :startTime) AND
                (:endTime IS NULL OR a.timestamp <= :endTime) AND
                a.table NOT IN ('DATABASECHANGELOG', 'DATABASECHANGELOGLOCK')
            """)
  List<String> findUniqueTablesByDatabaseAndTimeRange(
      @Param("database") String database,
      @Param("startTime") Long startTime,
      @Param("endTime") Long endTime);

  @Query(
      """
            SELECT DISTINCT a.primaryKey FROM AuditEvent a WHERE
            (:database IS NULL OR a.database = :database) AND
            (:tableName IS NULL OR a.table = :tableName) AND
            (:startTime IS NULL OR a.timestamp >= :startTime) AND
            (:endTime IS NULL OR a.timestamp <= :endTime)
            """)
  List<String> findUniqueKeysByDatabaseAndTableAndTimeRange(
      @Param("database") String database,
      @Param("tableName") String tableName,
      @Param("startTime") Long startTime,
      @Param("endTime") Long endTime);

  /** Find unique users by database and time range */
  @Query(
      """
            SELECT DISTINCT a.updatedBy FROM AuditEvent a WHERE
            (:database IS NULL OR a.database = :database) AND
            (:tableName IS NULL OR a.table = :tableName) AND
            (:startTime IS NULL OR a.timestamp >= :startTime) AND
            (:endTime IS NULL OR a.timestamp <= :endTime) AND
            a.updatedBy IS NOT NULL
            """)
  List<String> findUniqueUsersByDatabaseAndTimeRange(
      @Param("database") String database,
      @Param("tableName") String tableName,
      @Param("startTime") Long startTime,
      @Param("endTime") Long endTime);

  /** Find audit events with blank/null remarks and no operation snapshot */
  @Query(
      """
            SELECT a FROM AuditEvent a WHERE
            (a.remarks IS NULL OR a.remarks = '' OR a.remarks = 'null')
            ORDER BY a.timestamp DESC
            """)
  Page<AuditEvent> findAuditEventsWithBlankRemarks(Pageable pageable);

  /** Find second last snapshot for a primary key */
  @Query(
      """
            SELECT a FROM AuditEvent a WHERE
            a.database = :database AND a.table = :table AND a.primaryKey = :primaryKey AND a.timestamp != :timestamp
            ORDER BY a.timestamp DESC
            """)
  Page<AuditEvent> findSecondLastSnapshot(
      @Param("database") String database,
      @Param("table") String table,
      @Param("primaryKey") String primaryKey,
      @Param("timestamp") Long timestamp,
      Pageable pageable);

  @Query(
      """
            SELECT a FROM AuditEvent a WHERE
            (:databaseName IS NULL OR a.database = :databaseName) AND
            (:tableName IS NULL OR a.table = :tableName) AND
            (:key IS NULL OR a.primaryKey = :key) AND
            (:user IS NULL OR a.updatedBy = :user) AND
            (:startTime IS NULL OR a.timestamp >= :startTime) AND
            (:endTime IS NULL OR a.timestamp <= :endTime) AND
            a.table NOT IN ('DATABASECHANGELOG', 'DATABASECHANGELOGLOCK')
            ORDER BY a.id DESC
            """)
  Page<AuditEvent> findHistoryAuditEventsWithFilters(
      String databaseName,
      String tableName,
      String key,
      String user,
      Long startTime,
      Long endTime,
      PageRequest of);
}
