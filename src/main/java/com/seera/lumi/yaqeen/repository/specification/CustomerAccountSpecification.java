package com.seera.lumi.yaqeen.repository.specification;

import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

import com.seera.lumi.yaqeen.controller.request.CustomerAccountSearchRequestDTO;
import com.seera.lumi.yaqeen.domain.CustomerAccount;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor
public class CustomerAccountSpecification implements Specification<CustomerAccount> {

  private static final String ID = "id";
  private static final String SAP_ID = "sapId";
  private static final String CAR_PRO_ID = "carProId";
  private static final String ACCOUNT_TYPE = "accountType";
  private static final String REGION = "region";
  private static final String NAME = "name";
  private static final String EN = "en";
  private static final String WILDCARD = "%";
  private static final String IS_ACTIVE = "isActive";
  private static final String CUSTOMER_TYPE = "customerType";
  private final CustomerAccountSearchRequestDTO filter;

  @Override
  public Predicate toPredicate(
      Root<CustomerAccount> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    List<Predicate> predicate = new ArrayList<>();
    if (isNotEmpty(filter.getQuery())) {
      predicate.add(
          criteriaBuilder.like(
              criteriaBuilder.lower(root.get(NAME).get(EN)),
              WILDCARD + filter.getQuery().toLowerCase() + WILDCARD));
    }
    if (isNotEmpty(filter.getId())) {
      predicate.add(criteriaBuilder.equal(root.get(ID), filter.getId()));
    }
    if (isNotEmpty(filter.getSapId())) {
      predicate.add(criteriaBuilder.equal(root.get(SAP_ID), filter.getSapId()));
    }
    if (isNotEmpty(filter.getCarProId())) {
      predicate.add(criteriaBuilder.equal(root.get(CAR_PRO_ID), filter.getCarProId()));
    }
    if (isNotEmpty(filter.getAccountTypes())) {
      predicate.add(root.get(ACCOUNT_TYPE).in(filter.getAccountTypes()));
    }
    if (isNotEmpty(filter.getRegions())) {
      predicate.add(root.get(REGION).in(filter.getRegions()));
    }
    if (isNotEmpty(filter.getCustomerTypes())) {
      predicate.add(root.get(CUSTOMER_TYPE).in(filter.getCustomerTypes()));
    }
    if (isNotEmpty(filter.getActive())) {
      predicate.add(criteriaBuilder.equal(root.get(IS_ACTIVE), filter.getActive()));
    }
    return criteriaBuilder.and(predicate.toArray(Predicate[]::new));
  }
}
