package com.seera.lumi.yaqeen.repository;

import com.seera.lumi.yaqeen.domain.ReplicationJobConfiguration;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface ReplicationJobConfigurationRepository
    extends JpaRepository<ReplicationJobConfiguration, Long> {

  ReplicationJobConfiguration findByJobName(String jobName);

  Optional<ReplicationJobConfiguration> findByJobId(Integer jobId);
}
