package com.seera.lumi.yaqeen.repository;

import com.seera.lumi.yaqeen.domain.CustomerAccount;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;
import org.springframework.data.jpa.repository.Query;

public interface CustomerAccountRepository
    extends JpaRepository<CustomerAccount, Long>, JpaSpecificationExecutor<CustomerAccount> {

  @EntityGraph(value = "customer-account-entity-graph-with-billing-address")
  Optional<CustomerAccount> findById(Long id);

  @EntityGraph(value = "customer-account-entity-graph-with-billing-address")
  Optional<CustomerAccount> findBySapId(String sapId);

  @EntityGraph(value = "customer-account-entity-graph-with-billing-address")
  Optional<CustomerAccount> findByCarProId(String carProId);

  @Query("select distinct(c.accountType) from CustomerAccount c")
  List<String> findDistinctAccountTypes();

  @Query("select distinct(c.region) from CustomerAccount c")
  List<String> findDistinctAccountRegions();

  @EntityGraph(value = "customer-account-entity-graph-with-billing-address")
  Page<CustomerAccount> findAll(Specification<CustomerAccount> spec, Pageable pageable);
}
