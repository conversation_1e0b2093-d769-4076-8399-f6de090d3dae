package com.seera.lumi.yaqeen.controller.request;

import java.io.Serial;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class SearchRequest extends SortDTO {

  @Serial private static final long serialVersionUID = -600126160341757685L;

  private String query;

  @Override
  public String toString() {
    final StringBuilder sb =
        new StringBuilder(super.toString())
            .append("SearchRequestDTO{")
            .append("query='")
            .append(query)
            .append('\'')
            .append('}');
    return sb.toString();
  }
}
