package com.seera.lumi.yaqeen.controller.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgreementResponse implements Serializable {

  private Long id;

  private String plateNo;

  private String make;

  private String model;

  private Integer modelYear;

  private String group;

  private String carproAgreementId;

  private Boolean status;
}
