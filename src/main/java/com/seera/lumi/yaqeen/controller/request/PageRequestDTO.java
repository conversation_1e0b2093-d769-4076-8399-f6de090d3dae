package com.seera.lumi.yaqeen.controller.request;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
public class PageRequestDTO implements Serializable {

  @Serial private static final long serialVersionUID = -600126160341757685L;

  private Integer pageNumber = 0;
  private Integer pageSize = 10;

  public void nextPage() {
    pageNumber++;
  }

  @Override
  public String toString() {
    final StringBuilder sb =
        new StringBuilder("PageRequestDTO{")
            .append("page=")
            .append(pageNumber)
            .append(", size=")
            .append(pageSize)
            .append('}');
    return sb.toString();
  }
}
