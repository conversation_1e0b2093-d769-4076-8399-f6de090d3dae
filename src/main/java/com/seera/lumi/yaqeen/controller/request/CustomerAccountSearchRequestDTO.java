package com.seera.lumi.yaqeen.controller.request;

import java.util.List;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@Accessors(chain = true)
public class CustomerAccountSearchRequestDTO extends SearchRequest {

  Long id;
  String sapId;
  String carProId;
  List<String> accountTypes;
  List<String> regions;
  List<String> customerTypes;
  Boolean active;
}
