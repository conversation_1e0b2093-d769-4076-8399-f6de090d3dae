package com.seera.lumi.yaqeen.controller.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MultilingualDTO implements Serializable {

  private Long id;

  private String en;

  private String ar;
}
