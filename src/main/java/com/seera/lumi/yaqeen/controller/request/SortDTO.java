package com.seera.lumi.yaqeen.controller.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
public class SortDTO extends PageRequestDTO implements Serializable {

  private static final long serialVersionUID = 7393138539905976584L;

  private List<String> sort = List.of("id");

  private String order = "desc";

  public String getOrder() {
    return order;
  }

  public SortDTO setOrder(String order) {
    this.order = order;
    return this;
  }

  public String[] getSort() {
    return sort == null ? new String[] {"id"} : sort.toArray(String[]::new);
  }

  public SortDTO setSort(List<String> sort) {
    this.sort = sort;
    return this;
  }
}
