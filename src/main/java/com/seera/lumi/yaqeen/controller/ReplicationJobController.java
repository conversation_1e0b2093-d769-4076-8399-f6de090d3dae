package com.seera.lumi.yaqeen.controller;

import com.seera.lumi.yaqeen.controller.request.ReplicationJobInfo;
import com.seera.lumi.yaqeen.controller.request.ReplicationTaskInfo;
import com.seera.lumi.yaqeen.controller.resp.ReplicationJobConfigDTO;
import com.seera.lumi.yaqeen.domain.ReplicationJobConfiguration;
import com.seera.lumi.yaqeen.service.ReplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/replication/job")
public class ReplicationJobController {

  private final ReplicationService replicationService;

  @GetMapping("/config")
  public ResponseEntity<ReplicationJobConfigDTO> getJobByName(
      @RequestParam("jobName") String jobName) throws Exception {
    return ResponseEntity.ok(replicationService.getJobByName(jobName));
  }

  @GetMapping
  public ResponseEntity<?> getJobConfigById(@RequestParam("id") Integer jobId) throws Exception {
    return ResponseEntity.ok(replicationService.getJobConfigById(jobId));
  }

  @PostMapping("/update")
  public ResponseEntity<?> updateConfig(@RequestBody ReplicationJobConfiguration request)
      throws Exception {
    return ResponseEntity.ok(replicationService.updateConfig(request));
  }

  @PostMapping("/trigger")
  public ResponseEntity<Long> save(@RequestBody ReplicationJobInfo request) {
    log.info("In ReplicationJobController, jobTrigger called for request {}", request);
    return ResponseEntity.ok(replicationService.saveJobInfo(request));
  }

  @PostMapping("/execute")
  public void execute(@RequestBody ReplicationTaskInfo request) {
    log.info("In ReplicationJobController, execute called for request {}", request);
    replicationService.executeTask(request);
  }
}
