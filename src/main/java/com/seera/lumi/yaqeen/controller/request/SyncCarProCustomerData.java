package com.seera.lumi.yaqeen.controller.request;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

@Data
public class SyncCarProCustomerData {

  @JsonAlias("debitorCode")
  private String carProId;

  @JsonAlias("debitorName")
  private String nameEn;

  @JsonAlias("arabicName")
  private String nameAr;

  @JsonAlias("businessPlace")
  private String region;

  private String mailingHouseNumber;

  @JsonAlias("mailingAddress")
  private String mailingFullAddress;

  private String mailingCity;
  private String mailingState;
  private String mailingZip;
  private String mailingCountry;
  private String billingHouseNumber;

  @JsonAlias("billingAddress")
  private String billingFullAddress;

  private String billingCity;
  private String billingState;
  private String billingZip;
  private String billingCountry;

  @JsonAlias("contactNumber")
  private String phone;

  @JsonAlias("internetAddress")
  private String email;

  @JsonAlias("telex")
  private String sapId;

  @JsonAlias("accountNumber")
  private String vatNo;

  private String taxOffice;
  private String lease;

  @JsonAlias("createDate")
  private String creationDate;

  private String terminationDate;
  private String crNumber;
}
