package com.seera.lumi.yaqeen.controller;

import com.seera.lumi.yaqeen.controller.request.CreateLeaseCustomerRequest;
import com.seera.lumi.yaqeen.controller.request.CustomerAccountSearchRequestDTO;
import com.seera.lumi.yaqeen.controller.request.UpdateLeaseCustomerRequest;
import com.seera.lumi.yaqeen.controller.resp.CustomerAccountBasicResponse;
import com.seera.lumi.yaqeen.controller.resp.SearchResponse;
import com.seera.lumi.yaqeen.service.CustomerAccountService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/customer")
public class CustomerController {

  private final CustomerAccountService customerAccountService;

  @GetMapping("/accounts")
  public ResponseEntity<SearchResponse<CustomerAccountBasicResponse>> getAllCustomerAccounts(
      CustomerAccountSearchRequestDTO searchRequest) {
    Page<CustomerAccountBasicResponse> response = customerAccountService.findAll(searchRequest);
    return ResponseEntity.ok(new SearchResponse<>(response));
  }

  @GetMapping("/account")
  public ResponseEntity get(
      @RequestParam(value = "id", required = false) Long id,
      @RequestParam(value = "sapId", required = false) String sapId,
      @RequestParam(value = "carProId", required = false) String carProId) {
    if (id != null) {
      return ResponseEntity.ok(customerAccountService.findById(id));
    } else if (sapId != null) {
      return ResponseEntity.ok(customerAccountService.findBySapId(sapId));
    } else if (carProId != null) {
      return ResponseEntity.ok(customerAccountService.findByCarProId(carProId));
    }
    return ResponseEntity.badRequest().build();
  }

  @PostMapping("/account")
  public ResponseEntity create(@RequestBody CreateLeaseCustomerRequest createLeaseCustomerRequest) {
    try {
      return ResponseEntity.ok(
          customerAccountService.createCustomerAccount(createLeaseCustomerRequest));
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body("An unexpected error occurred: " + e.getMessage());
    }
  }

  @PatchMapping("/account")
  public ResponseEntity update(@RequestBody UpdateLeaseCustomerRequest updateLeaseCustomerRequest) {
    try {
      return ResponseEntity.ok(
          customerAccountService.updateCustomerAccount(updateLeaseCustomerRequest));
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body("An unexpected error occurred: " + e.getMessage());
    }
  }

  @DeleteMapping("/account/{id}")
  public ResponseEntity delete(@PathVariable("id") Long id) {
    customerAccountService.deactivateCustomerAccount(id);
    return ResponseEntity.ok().build();
  }

  @GetMapping("/account/types")
  public ResponseEntity<List<String>> getAllCustomerAccountTypes() {
    return ResponseEntity.ok(customerAccountService.getAllCustomerAccountTypes());
  }

  @GetMapping("/account/regions")
  public ResponseEntity<List<String>> getAllCustomerAccountRegions() {
    return ResponseEntity.ok(customerAccountService.getAllCustomerAccountRegions());
  }
}
