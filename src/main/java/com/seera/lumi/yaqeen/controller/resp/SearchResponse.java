package com.seera.lumi.yaqeen.controller.resp;

import java.util.List;
import lombok.Data;
import org.springframework.data.domain.Page;

@Data
public class SearchResponse<T> {

  private final long total;
  private final List<T> data;

  public SearchResponse(Page<T> page) {
    this.total = page.getTotalElements();
    this.data = page.getContent();
  }

  public SearchResponse(List<T> list) {
    this.total = list.size();
    this.data = list;
  }
}
