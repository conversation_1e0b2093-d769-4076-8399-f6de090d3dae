package com.seera.lumi.yaqeen.controller.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerAccountBasicResponse implements Serializable {

  private Long id;
  private MultilingualDTO name;
  private String region;
  private AccountAddressDTO address;
  private String accountType;
  private String carProId;
}
