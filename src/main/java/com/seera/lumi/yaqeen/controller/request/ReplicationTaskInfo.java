package com.seera.lumi.yaqeen.controller.request;

import lombok.Data;

@Data
public class ReplicationTaskInfo {
  private String time;
  private Long replicationJobId;
  private Integer taskId;
  private Integer jobId;
  private String jobName;
  // this parameter will use for fetching the records for given time-period
  private String from;
  private String to;

  @Override
  public String toString() {
    return "ReplicationTaskInfo{"
        + "Replication-Job-Id="
        + replicationJobId
        + ", task-id="
        + taskId
        + ", job-name='"
        + jobName
        + '\''
        + '}';
  }
}
