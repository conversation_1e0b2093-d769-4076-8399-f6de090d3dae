package com.seera.lumi.yaqeen.controller.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerAccountResponse implements Serializable {

  private Long id;
  private MultilingualDTO name;
  private String carProId;
  private String sapId;
  private Boolean isActive;
  private LocalDate createdOn;
}
