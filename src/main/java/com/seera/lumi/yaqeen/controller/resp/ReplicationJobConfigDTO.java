package com.seera.lumi.yaqeen.controller.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReplicationJobConfigDTO {

  Long jobId;
  String jobName;
  String sourceName;
  String jobDescription;
  String columns;
  Boolean enabled;
  String table;
  String filter;
  String partitionColumn;
  Integer batchSize;
  String kafkaTopicName;
  String kafkaPartitionKey;
  Integer lastXDays;
  List<String> cacheKeys;
}
