package com.seera.lumi.yaqeen.controller;

import com.seera.lumi.yaqeen.reports.ReportScheduler;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Component
@RestController
@RequestMapping("/api/reports")
@RequiredArgsConstructor
public class ReportController {

    private final ReportScheduler reportScheduler;

    @GetMapping("/generate-all-reports")
    public String generateAllReports() {
        reportScheduler.generateAllReports();
        return "All Reports triggered successfully!";
    }

    @GetMapping("/generate-traffic-fine-reports")
    public String generateTrafficFineReports() {
        reportScheduler.generateTrafficFineReports();
        return "Traffic Fine Reports triggered successfully!";
    }
}