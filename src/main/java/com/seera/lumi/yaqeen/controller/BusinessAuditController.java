package com.seera.lumi.yaqeen.controller;

import com.seera.lumi.yaqeen.controller.resp.UniqueBusinessAuditDataResponse;
import com.seera.lumi.yaqeen.domain.BusinessAuditEvent;
import com.seera.lumi.yaqeen.service.BusinessAuditEventService;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/business-audit")
@RequiredArgsConstructor
public class BusinessAuditController {

  private final BusinessAuditEventService businessAuditEventService;

  /** Get recent business audit events with pagination support Default pageSize = 10 */
  @GetMapping("/recent")
  public ResponseEntity<Page<BusinessAuditEvent>> getRecentBusinessAuditEvents(
      @RequestParam(required = false) String resourceType,
      @RequestParam(required = false) String resourceValue,
      @RequestParam(required = false) String action,
      @RequestParam(required = false) String clientId,
      @RequestParam(required = false) String userId,
      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
          LocalDateTime startTime,
      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
          LocalDateTime endTime,
      @RequestParam(required = false) Boolean success,
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int pageSize) {

    log.info(
        "Fetching Business Audit Events with resourceType: {}, resourceValue: {}, action: {}, userId: {}, startTime: {}, endTime: {}, success: {}, clientId: {}",
        resourceType,
        resourceValue,
        action,
        userId,
        startTime,
        endTime,
        success,
        clientId);

    // Validation - Page aur pageSize
    if (page < 0) {
      log.error("Page number cannot be negative: {}", page);
      return ResponseEntity.badRequest().build();
    }

    // Validation - Time range
    if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
      log.error("Invalid time range: startTime {} should be before endTime {}", startTime, endTime);
      return ResponseEntity.badRequest().build();
    }

    return ResponseEntity.ok(
        businessAuditEventService.getRecentBusinessAuditEvents(
            userId,
            action,
            resourceType,
            resourceValue,
            startTime,
            endTime,
            success,
            clientId,
            page,
            pageSize));
  }

  @GetMapping("/history")
  public ResponseEntity<Page<BusinessAuditEvent>> getBusinessAuditHistory(
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int pageSize,
      @RequestParam String userId,
      @RequestParam String action,
      @RequestParam String resourceType,
      @RequestParam(required = false) String clientId,
      @RequestParam(required = false) String resourceValue,
      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
          LocalDateTime startTime,
      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
          LocalDateTime endTime,
      @RequestParam(required = false) Boolean success) {

    log.info(
        "Fetching Business Audit History with page: {}, pageSize: {}, userId: {}, action: {}, resourceType: {}, resourceValue: {}, startTime: {}, endTime: {}, success: {}",
        page,
        pageSize,
        userId,
        action,
        resourceType,
        resourceValue,
        startTime,
        endTime,
        success);

    // Validation - Page aur pageSize
    if (page < 0) {
      log.error("Page number cannot be negative: {}", page);
      return ResponseEntity.badRequest().build();
    }

    if (pageSize <= 0 || pageSize > 100) {
      log.error("Page size should be between 1 and 100: {}", pageSize);
      return ResponseEntity.badRequest().build();
    }

    // Validation - Time range
    if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
      log.error("Invalid time range: startTime {} should be before endTime {}", startTime, endTime);
      return ResponseEntity.badRequest().build();
    }

    return ResponseEntity.ok(
        businessAuditEventService.getBusinessAuditEventHistory(
            userId,
            action,
            resourceType,
            resourceValue,
            startTime,
            endTime,
            success,
            clientId,
            page,
            pageSize));
  }

  @GetMapping("/filters")
  public ResponseEntity<UniqueBusinessAuditDataResponse> getBusinessAuditDataFilters(
    @RequestParam(required = false) String resourceType,
    @RequestParam(required = false) String resourceValue,
    @RequestParam(required = false) String action,
    @RequestParam(required = false) Boolean success,
    @RequestParam(required = false) String userId,
    @RequestParam(required = false) String clientId,
      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
          LocalDateTime startTime,
      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
          LocalDateTime endTime) {

    log.info(
        "Fetching Business Audit Data Filters with userId: {}, action: {}, resourceType: {}, resourceValue: {}, success: {}, clientId: {}, startTime: {}, endTime: {}",
        userId,
        action,
        resourceType,
        resourceValue,
        success,
        clientId,
        startTime,
        endTime);

    if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
      log.error("Invalid time range: startTime {} should be before endTime {}", startTime, endTime);
      return ResponseEntity.badRequest().body(UniqueBusinessAuditDataResponse.builder().build());
    }
    List<String> uniqueResourceTypes = businessAuditEventService.getUniqueResourceTypes(userId, action, resourceValue, success, clientId, startTime, endTime);
    List<String> uniqueResourceValues = StringUtils.isNotBlank(resourceType) ? businessAuditEventService.getUniqueResourceValues(userId, action, resourceType, success, clientId, startTime, endTime) : List.of();
    List<String> uniqueActions = businessAuditEventService.getUniqueActions(userId, resourceType, resourceValue, success, clientId, startTime, endTime);
    List<String> uniqueClientIds = businessAuditEventService.getUniqueClientIds(userId, action, resourceType, resourceValue, success, startTime, endTime);
    List<String> uniqueUserIds = businessAuditEventService.getUniqueUserIds(action, resourceType, resourceValue, success, clientId, startTime, endTime);
    

    UniqueBusinessAuditDataResponse response =
        UniqueBusinessAuditDataResponse.builder()
            .userIds(uniqueUserIds != null ? uniqueUserIds : List.of())
            .actions(uniqueActions != null ? uniqueActions : List.of())
            .resourceTypes(uniqueResourceTypes != null ? uniqueResourceTypes : List.of())
            .resourceValues(uniqueResourceValues != null ? uniqueResourceValues : List.of())
            .clientIds(uniqueClientIds != null ? uniqueClientIds : List.of())
            .build();

    return ResponseEntity.ok(response);
  }

  @GetMapping("/search")
  public ResponseEntity<Page<BusinessAuditEvent>> searchBusinessAuditEvents(
          @RequestParam(defaultValue = "0") int page,
          @RequestParam(defaultValue = "10") int pageSize,
          @RequestParam(required = false) String userId,
          @RequestParam(required = false) String action,
          @RequestParam(required = false) String resourceType,
          @RequestParam(required = false) String resourceValue,
          @RequestParam(required = false) Boolean success,
          @RequestParam(required = false) String traceId,
          @RequestParam(required = false) String auditId,
          @RequestParam(required = false) String clientId,
      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
          LocalDateTime startTime,
      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
          LocalDateTime endTime
      ) {

    log.info(
        "Searching Business Audit Events with auditId: {}, traceId: {}, userId: {}, action: {}, resourceType: {}, resourceValue: {}, success: {}",
        auditId,
        traceId,
        userId,
        action,
        resourceType,
        resourceValue,
        success);

    // Validation - Page aur pageSize
    if (page < 0) {
      log.error("Page number cannot be negative: {}", page);
      return ResponseEntity.badRequest().build();
    }

    if (pageSize <= 0 || pageSize > 100) {
      log.error("Page size should be between 1 and 100: {}", pageSize);
      return ResponseEntity.badRequest().build();
    }

    // Validation - Time range
    if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
      log.error("Invalid time range: startTime {} should be before endTime {}", startTime, endTime);
      return ResponseEntity.badRequest().build();
    }

    // Handle specific searches first
    if (auditId != null) {
      BusinessAuditEvent event = businessAuditEventService.findByAuditId(auditId);
      if (event != null) {
        return ResponseEntity.ok(new PageImpl<>(List.of(event), PageRequest.of(page, pageSize), 1));
      } else {
        return ResponseEntity.ok(new PageImpl<>(List.of(), PageRequest.of(page, pageSize), 0));
      }
    }

    if (traceId != null) {
      List<BusinessAuditEvent> events = businessAuditEventService.findByTraceId(traceId);
      int start = page * pageSize;
      int end = Math.min(start + pageSize, events.size());
      List<BusinessAuditEvent> pagedEvents = events.subList(start, end);
      return ResponseEntity.ok(
          new PageImpl<>(pagedEvents, PageRequest.of(page, pageSize), events.size()));
    }

    // Default search with filters
    return ResponseEntity.ok(
        businessAuditEventService.getRecentBusinessAuditEvents(
            userId,
            action,
            resourceType,
            resourceValue,
            startTime,
            endTime,
            success,
            clientId,
            page,
            pageSize));
  }
}
