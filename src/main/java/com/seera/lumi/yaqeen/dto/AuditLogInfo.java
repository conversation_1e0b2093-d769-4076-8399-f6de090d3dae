package com.seera.lumi.yaqeen.dto;

import java.util.Map;

import com.google.api.client.util.ArrayMap;
import lombok.Data;

@Data
public class AuditLogInfo {
  private Map<String, Object> before;
  private Map<String, Object> after;
  private Source source;
  private String op;
  private Long ts_ms;
  private Transaction transaction;

  public Map<String, Object> getAfter() {
    return after == null ? new ArrayMap<>() : after;
  }

  // Nested Source class
  @Data
  public static class Source {
    private String version;
    private String connector;
    private String name;
    private Long ts_ms;
    private String snapshot;
    private String db;
    private String sequence;
    private String table;
    private Long server_id;
    private String gtid;
    private String file;
    private Long pos;
    private Long row;
    private Long thread;
    private String query;
  }

  // Nested Transaction class
  @Data
  public static class Transaction {
    private String id;
    private Long total_order;
    private Long data_collection_order;
  }
}
