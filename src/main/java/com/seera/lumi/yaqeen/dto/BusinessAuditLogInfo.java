package com.seera.lumi.yaqeen.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BusinessAuditLogInfo {

  private String auditId;
  private String userId;
  private String action;
  private String resourceType;
  private String resourceValue;
  private String description;
  private String methodName;
  private String className;
  private Object parameters;
  private Object result;
  private String traceId;
  private String clientId;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime timestamp;

  private String httpMethod;
  private String requestUri;
  private String userAgent;
  private String ipAddress;
  private Boolean success;
  private String errorMessage;
  private Long executionTimeMs;
}
