package com.seera.lumi.yaqeen.mapper;

import static com.seera.lumi.yaqeen.enums.CustomerType.LEASE;
import static com.seera.lumi.yaqeen.enums.CustomerType.RENTAL;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

import com.seera.lumi.yaqeen.controller.request.BaseLeaseCustomerRequest;
import com.seera.lumi.yaqeen.controller.request.SyncCarProCustomerData;
import com.seera.lumi.yaqeen.controller.resp.AccountAddressDTO;
import com.seera.lumi.yaqeen.controller.resp.CustomerAccountBasicResponse;
import com.seera.lumi.yaqeen.controller.resp.CustomerAccountResponse;
import com.seera.lumi.yaqeen.domain.AccountAddress;
import com.seera.lumi.yaqeen.domain.Customer;
import com.seera.lumi.yaqeen.domain.CustomerAccount;
import com.seera.lumi.yaqeen.domain.Multilingual;
import com.seera.lumi.yaqeen.enums.CustomerType;
import com.seera.lumi.yaqeen.util.DateUtil;
import java.time.LocalDateTime;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    uses = {MultilingualMapper.class})
public interface LeaseCustomerMapper {

  @Mapping(source = "nameEn", target = "name.en")
  @Mapping(source = "nameAr", target = "name.ar")
  Customer toCustomer(BaseLeaseCustomerRequest customerData);

  @Mapping(source = "nameEn", target = "name.en")
  @Mapping(source = "nameAr", target = "name.ar")
  @Mapping(source = "crNumber", target = "crNo")
  @Mapping(source = "customerData", target = "vatNo", qualifiedByName = "mapVatNo")
  Customer toCustomer(SyncCarProCustomerData customerData);

  @Mapping(source = "creationDate", target = "createdOn", qualifiedByName = "mapCarProDate")
  @Mapping(source = "terminationDate", target = "isActive", qualifiedByName = "customerActive")
  @Mapping(
      source = "customerData",
      target = "billingAddress",
      qualifiedByName = "syncCarProCustomerDataToBillingAddress")
  @Mapping(
      source = "customerData",
      target = "name",
      qualifiedByName = "syncCarProCustomerDataToMultilingual")
  @Mapping(
      source = "customerData.lease",
      target = "customerType",
      qualifiedByName = "mapCustomerType")
  CustomerAccount toCustomerAccount(SyncCarProCustomerData customerData);

  default CustomerAccount buildCustomerAccount(BaseLeaseCustomerRequest request) {
    CustomerAccount customerAccount = new CustomerAccount();
    if (request != null) {
      customerAccount.setName(toMultilingual(request));
      customerAccount.setEmail(request.getEmail());
      customerAccount.setPhone(request.getPhoneNo());
      customerAccount.setBillingAddress(toBillingAddress(request));
      customerAccount.setSapId(request.getSapId());
    }
    return customerAccount;
  }

  default void updateCustomerAccountFromBaseLeaseRequest(
      BaseLeaseCustomerRequest request, CustomerAccount account) {
    if (nonNull(request)) {
      account.setEmail(request.getEmail());
      account.setPhone(request.getPhoneNo());
      account.setSapId(request.getSapId());
    }
  }

  default void updateCustomerAccount(SyncCarProCustomerData request, CustomerAccount account) {
    if (nonNull(request)) {
      account.setIsActive(customerActive(request.getTerminationDate()));
    }
  }

  @Named("mapVatNo")
  default Long mapVatNo(SyncCarProCustomerData customerData) {
    try {
      if (isNotEmpty(customerData.getVatNo())) {
        return Long.parseLong(customerData.getVatNo());
      } else if (isNotEmpty(customerData.getTaxOffice())) {
        return Long.parseLong(customerData.getTaxOffice());
      }
    } catch (Exception e) {
      if (isNotEmpty(customerData.getTaxOffice())) {
        return Long.parseLong(customerData.getTaxOffice());
      }
    }
    return null;
  }

  @Named("mapCarProDate")
  default LocalDateTime mapCarProDate(String date) {
    return DateUtil.convertDateWithoutTime(date, "yyyyMMdd");
  }

  @Named("customerActive")
  default Boolean customerActive(String terminationDate) {
    return isNotEmpty(terminationDate) && "********".equals(terminationDate) ? true : false;
  }

  @Named("mapCustomerType")
  default CustomerType mapCustomerType(String lease) {
    return isNotEmpty(lease) && "Y".equalsIgnoreCase(lease) ? LEASE : RENTAL;
  }

  @Mapping(source = "nameEn", target = "en")
  @Mapping(source = "nameAr", target = "ar")
  Multilingual toMultilingual(BaseLeaseCustomerRequest request);

  @Mapping(source = "billingHouseNumber", target = "houseNo")
  @Mapping(source = "billingFullAddress", target = "fullAddress.en")
  @Mapping(source = "billingCity", target = "city")
  @Mapping(source = "billingZip", target = "zip")
  @Mapping(source = "billingCountry", target = "country")
  AccountAddress toBillingAddress(BaseLeaseCustomerRequest request);

  @Mapping(source = "mailingHouseNumber", target = "houseNo")
  @Mapping(source = "mailingFullAddress", target = "fullAddress.en")
  @Mapping(source = "mailingCity", target = "city")
  @Mapping(source = "mailingZip", target = "zip")
  @Mapping(source = "mailingCountry", target = "country")
  AccountAddress toMailingAddress(BaseLeaseCustomerRequest request);

  @Mapping(source = "billingHouseNumber", target = "houseNo")
  @Mapping(source = "billingFullAddress", target = "fullAddress.en")
  @Mapping(source = "billingCity", target = "city")
  @Mapping(source = "billingZip", target = "zip")
  @Mapping(source = "billingCountry", target = "country")
  @Named("syncCarProCustomerDataToBillingAddress")
  AccountAddress toBillingAddress(SyncCarProCustomerData request);

  @Mapping(source = "nameEn", target = "en")
  @Mapping(source = "nameAr", target = "ar")
  @Named("syncCarProCustomerDataToMultilingual")
  Multilingual toMultilingual(SyncCarProCustomerData request);

  @Mapping(source = "customerAccount.billingAddress", target = "address")
  CustomerAccountBasicResponse toBasicResponse(CustomerAccount customerAccount);

  CustomerAccountResponse toResponse(CustomerAccount customerAccount);

  AccountAddressDTO toDTO(AccountAddress accountAddress);
}
