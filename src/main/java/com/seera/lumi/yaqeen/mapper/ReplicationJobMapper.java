package com.seera.lumi.yaqeen.mapper;

import com.seera.lumi.yaqeen.controller.request.ReplicationJobInfo;
import com.seera.lumi.yaqeen.controller.resp.ReplicationJobConfigDTO;
import com.seera.lumi.yaqeen.domain.ReplicationJobConfiguration;
import com.seera.lumi.yaqeen.domain.ReplicationJobHistory;
import com.seera.lumi.yaqeen.domain.ReplicationTaskHistory;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import java.time.LocalDateTime;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ReplicationJobMapper {

  ReplicationJobConfigDTO mapReplicationJobConfigToDTO(ReplicationJobConfiguration configuration);

  ReplicationJobHistory mapReplicationJobHistory(ReplicationJobInfo request);

  ReplicationTaskHistory mapReplicationTaskHistory(
      Long replicationJobId,
      Integer jobId,
      Integer taskId,
      Integer fetchedR<PERSON>ord,
      Integer updatedRecord,
      String jobStatus,
      String remark,
      LocalDateTime startTime,
      LocalDateTime endTime);

  ReplicationJobConfiguration updateConfig(
      @MappingTarget ReplicationJobConfiguration existingConfig,
      ReplicationJobConfiguration request);
}
