package com.seera.lumi.yaqeen.mapper;

import com.seera.lumi.yaqeen.controller.resp.MultilingualDTO;
import com.seera.lumi.yaqeen.domain.Multilingual;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MultilingualMapper {

  @Mapping(source = "id", target = "id", ignore = true)
  void updateEntity(@MappingTarget Multilingual multilingual, MultilingualDTO request);

  @Mapping(source = "id", target = "id", ignore = true)
  Multilingual toNewEntity(MultilingualDTO multilingualDTO);

  MultilingualDTO toResponse(Multilingual multilingual);
}
