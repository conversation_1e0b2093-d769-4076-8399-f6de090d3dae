package com.seera.lumi.yaqeen.listeners;

import com.seera.lumi.yaqeen.dto.AuditLogInfo;
import com.seera.lumi.yaqeen.service.AuditLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuditLogListener {

  private final AuditLogService auditLogService;

  @KafkaListener(
      topics = {"${kafka.topic.audit.log}"},
      groupId = "audit-event-listener",
      concurrency = "${kafka.listen.concurrency}",
      containerFactory = "kafkaListenerContainerFactory",
      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(@Payload AuditLogInfo event) {
    try {
      // log.info("Message received for audit log : {}", event);
      auditLogService.processAuditLog(event);
    } catch (Exception ex) {
      log.error("Error while listening audit-log event", ex);
    }
  }
}
