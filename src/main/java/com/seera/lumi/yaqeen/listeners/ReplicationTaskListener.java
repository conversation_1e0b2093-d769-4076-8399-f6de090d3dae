package com.seera.lumi.yaqeen.listeners;

import com.seera.lumi.yaqeen.controller.request.ReplicationTaskInfo;
import com.seera.lumi.yaqeen.service.ReplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReplicationTaskListener {

  private final ExecutorService executorService = Executors.newFixedThreadPool(10);
  private final ReplicationService replicationService;

  @KafkaListener(
      topics = {"${kafka.topic.replication.task}"},
      groupId = "execute-replication-task",
      concurrency = "${kafka.listen.concurrency}",
      containerFactory = "kafkaListenerContainerFactory",
      autoStartup = "${kafka.listen.auto.start:true}")
  public void listen(@Payload ReplicationTaskInfo event) {
    try {
      log.info("Message received for replication task : {}", event);
      executorService.submit(() -> replicationService.executeTask(event));
    } catch (Exception ex) {
      log.error("Error while listening replication-task event", ex);
    }
  }
}
