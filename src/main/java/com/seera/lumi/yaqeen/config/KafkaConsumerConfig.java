package com.seera.lumi.yaqeen.config;

import static org.springframework.kafka.retrytopic.RetryTopicConstants.DEFAULT_DLT_SUFFIX;

import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.BytesDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.DeadLetterPublishingRecoverer;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.kafka.support.converter.StringJsonMessageConverter;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.util.backoff.FixedBackOff;

@RequiredArgsConstructor
@EnableKafka
@Configuration
public class KafkaConsumerConfig {

  private final KafkaTemplate coreKafkaDLTTemplate;
  private final KafkaTemplate coreKafkaBatchDLTTemplate;
  @Value("${spring.kafka.bootstrap-servers}")
  private String bootstrapAddress;
  @Value("${spring.kafka.consumer.group-id}")
  private String groupId;

  @Bean
  public ConsumerFactory consumerFactory() {
    return new DefaultKafkaConsumerFactory<>(consumerConfigs());
  }

  @Bean
  public ConsumerFactory batchConsumerFactory() {
    Map<String, Object> props = consumerConfigs();
    props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 1000);
    props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, BytesDeserializer.class);
    return new DefaultKafkaConsumerFactory<>(props);
  }

  @Bean
  public Map<String, Object> consumerConfigs() {
    Map<String, Object> props = new HashMap<>();
    props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);
    props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
    props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
    props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
    props.put(JsonDeserializer.TRUSTED_PACKAGES, "*");
    return props;
  }

  @Bean
  public ConcurrentKafkaListenerContainerFactory<String, Object> kafkaListenerContainerFactory() {
    ConcurrentKafkaListenerContainerFactory<String, Object> factory =
        new ConcurrentKafkaListenerContainerFactory<>();
    factory.setCommonErrorHandler(getDefaultErrorHandler());
    return addDefaultConfigurations(factory);
  }

  @Bean
  public ConcurrentKafkaListenerContainerFactory<String, Object>
      kafkaBatchListenerContainerFactory() {
    ConcurrentKafkaListenerContainerFactory<String, Object> factory =
        new ConcurrentKafkaListenerContainerFactory<>();
    factory.setBatchListener(true);
    factory.setConsumerFactory(batchConsumerFactory());
    factory.setCommonErrorHandler(getDefaultBatchErrorHandler());
    return factory;
  }

  @Bean
  public ConcurrentKafkaListenerContainerFactory<String, Object>
      kafkaDLTListenerContainerFactory() {
    ConcurrentKafkaListenerContainerFactory<String, Object> factory =
        new ConcurrentKafkaListenerContainerFactory<>();
    return addDefaultConfigurations(factory);
  }

  private ConcurrentKafkaListenerContainerFactory<String, Object> addDefaultConfigurations(
      ConcurrentKafkaListenerContainerFactory<String, Object> factory) {
    factory.setConsumerFactory(consumerFactory());
    factory.setRecordMessageConverter(new StringJsonMessageConverter());
    factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.RECORD);
    return factory;
  }

  private DeadLetterPublishingRecoverer getDeadLetterPublishingRecoverer(
      KafkaTemplate kafkaTemplate) {
    return new DeadLetterPublishingRecoverer(
        kafkaTemplate,
        (cr, e) -> new TopicPartition(cr.topic() + DEFAULT_DLT_SUFFIX, cr.partition()));
  }

  private DefaultErrorHandler getDefaultErrorHandler() {
    return new DefaultErrorHandler(
        getDeadLetterPublishingRecoverer(coreKafkaDLTTemplate), new FixedBackOff(10000L, 3L));
  }

  private DefaultErrorHandler getDefaultBatchErrorHandler() {
    return new DefaultErrorHandler(
        getDeadLetterPublishingRecoverer(coreKafkaBatchDLTTemplate), new FixedBackOff(0L, 0L));
  }
}
