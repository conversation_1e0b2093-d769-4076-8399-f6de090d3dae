package com.seera.lumi.yaqeen.config;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.drive.Drive;
import com.google.api.services.drive.DriveScopes;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class GoogleDriveConfig {

  @Bean
  public Drive drive() throws GeneralSecurityException, IOException {
    try {
      log.info("Initializing Google Drive client...");
      GoogleCredentials credentials =
          GoogleCredentials.fromStream(
                  getClass().getResourceAsStream("/google-drive-service-account.json"))
              .createScoped(Collections.singleton(DriveScopes.DRIVE_FILE));

      Drive drive =
          new Drive.Builder(
                  GoogleNetHttpTransport.newTrustedTransport(),
                  GsonFactory.getDefaultInstance(),
                  new HttpCredentialsAdapter(credentials))
              .setApplicationName("Lumi Core Clockwork")
              .build();

      // Test the connection
      drive.files().list().setPageSize(1).execute();
      log.info("Google Drive client initialized successfully");

      return drive;
    } catch (Exception e) {
      log.error("Failed to initialize Google Drive client. Please ensure:", e);
      log.error("1. Google Drive API is enabled in your project");
      log.error("2. Service account has proper permissions");
      log.error("3. Service account key file is present in resources folder");
      throw e;
    }
  }
}
