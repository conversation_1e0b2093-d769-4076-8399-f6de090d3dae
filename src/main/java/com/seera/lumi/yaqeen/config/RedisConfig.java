package com.seera.lumi.yaqeen.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
@RequiredArgsConstructor
public class RedisConfig {

  private final ObjectMapper objectMapper;
  @Value("${spring.data.redis.host}")
  private String redisHost;
  @Value("${spring.data.redis.port}")
  private Integer redisPort;
  @Value("${spring.data.redis.password}")
  private String redisPass;
  @Value("${spring.data.redis.ssl.enabled}")
  private Boolean redisSsl;
  @Value("${spring.data.redis.timeout}")
  private Integer connectTimeout;

  @Bean
  @Primary
  JedisConnectionFactory jedisConnectionFactory() {
    RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
    redisStandaloneConfiguration.setHostName(redisHost);
    redisStandaloneConfiguration.setPort(redisPort);
    redisStandaloneConfiguration.setDatabase(8);
    redisStandaloneConfiguration.setPassword(redisPass);
    JedisClientConfiguration.DefaultJedisClientConfigurationBuilder jedisClientConfiguration =
        (JedisClientConfiguration.DefaultJedisClientConfigurationBuilder)
            JedisClientConfiguration.builder();
    jedisClientConfiguration.usePooling();
    if (Boolean.TRUE.equals(redisSsl)) {
      jedisClientConfiguration.useSsl();
    }
    return new JedisConnectionFactory(
        redisStandaloneConfiguration, jedisClientConfiguration.build());
  }

  @Bean
  RedisTemplate<String, Object> redisTemplate() {
    final RedisTemplate<String, Object> template = new RedisTemplate<>();
    template.setConnectionFactory(jedisConnectionFactory());
    template.setKeySerializer(new StringRedisSerializer());
    template.setHashValueSerializer(new Jackson2JsonRedisSerializer<>(Object.class));
    template.setValueSerializer(new Jackson2JsonRedisSerializer<>(Object.class));
    return template;
  }
}
