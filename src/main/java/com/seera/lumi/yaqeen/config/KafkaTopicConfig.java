package com.seera.lumi.yaqeen.config;

import static org.springframework.kafka.retrytopic.RetryTopicConstants.DEFAULT_DLT_SUFFIX;

import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.KafkaAdmin;

@EnableKafka
@Configuration
public class KafkaTopicConfig {

  @Value("${kafka.topic.vehicle.financial.data}")
  private String vehicleFinancialDataTopic;

  @Value("${spring.kafka.bootstrap-servers}")
  private String bootstrapAddress;

  @Value("${spring.cloud.stream.kafka.binder.minPartitionCount}")
  private Integer partition;

  @Value("${spring.cloud.stream.kafka.binder.replicationFactor}")
  private Integer replication;

  @Value("${kafka.topic.customer.data}")
  private String syncCustomerDataTopic;

  @Value("${kafka.topic.replication.task}")
  private String replicatingTaskTopic;

  @Bean
  public KafkaAdmin kafkaAdmin() {
    Map<String, Object> configs = new HashMap<>();
    configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);
    return new KafkaAdmin(configs);
  }

  @Bean
  public NewTopic vehicleFinancialDataTopic() {
    return createTopic(vehicleFinancialDataTopic);
  }

  public NewTopic syncCustomerDataTopic() {
    return createTopic(syncCustomerDataTopic);
  }

  @Bean
  public NewTopic syncCustomerDataDLTTopic() {
    return createDltTopic(syncCustomerDataTopic);
  }

  @Bean
  public NewTopic replicatingTaskTopic() {
    return createTopic(replicatingTaskTopic);
  }

  @Bean
  public NewTopic replicatingTaskDLTTopic() {
    return createDltTopic(replicatingTaskTopic);
  }

  private NewTopic createTopic(String name) {
    return TopicBuilder.name(name).partitions(partition).replicas(replication).build();
  }

  private NewTopic createDltTopic(String name) {
    return createTopic(name + DEFAULT_DLT_SUFFIX);
  }
}
