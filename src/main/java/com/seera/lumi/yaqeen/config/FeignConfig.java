package com.seera.lumi.yaqeen.config;

import feign.Retryer;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableFeignClients(basePackages = "com.seera.lumi.yaqeen")
public class FeignConfig {

  @Bean
  public Retryer feignRetryer() {
    return new Retryer.Default(5000L, 3000L, 3);
  }
}
