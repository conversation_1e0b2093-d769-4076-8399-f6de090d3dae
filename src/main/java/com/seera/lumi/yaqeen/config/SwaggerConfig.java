package com.seera.lumi.yaqeen.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@ConditionalOnExpression("${config.swagger-enabled:true}")
@Configuration
public class SwaggerConfig {

  @Value("${spring.application.name}")
  private String appName;

  @Value("${info.app.version}")
  private String appVersion;

  @Value("${info.app.description}")
  private String appDescription;

  @Value("${springdoc.swagger-ui.host}")
  private String swaggerHost;

  @Value("${swagger-context}")
  private String swaggerContext;

  @Bean
  public OpenAPI customOpenAPI() {
    var contact = new Contact();
    contact.setName(appName);
    contact.setEmail("<EMAIL>");
    contact.setUrl("https://lumirental.com");

    final String securitySchemeName = "Authorization";
    Server server = new Server();
    server.setUrl(swaggerContext);

    return new OpenAPI()
        .servers(List.of(server))
        .components(
            new Components()
                .addSecuritySchemes(
                    securitySchemeName,
                    new SecurityScheme()
                        .name(securitySchemeName)
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT")))
        .info(
            new Info()
                .contact(contact)
                .title(appName)
                .description("Yaqeen Core Business REST API")
                .version(appVersion))
        .addSecurityItem(new SecurityRequirement().addList(securitySchemeName));
  }
}
