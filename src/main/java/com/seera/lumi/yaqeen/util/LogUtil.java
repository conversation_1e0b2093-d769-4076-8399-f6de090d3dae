package com.seera.lumi.yaqeen.util;

import io.micrometer.tracing.Tracer;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class LogUtil {

  private static final String FORMAT = "%s-%s";

  public static String getTraceId(Tracer tracer) {
    try {
      String traceId = tracer.currentSpan().context().traceId();
      String spanId = tracer.currentSpan().context().spanId();
      return String.format(FORMAT, spanId, traceId);
    } catch (Exception var4) {
      return "";
    }
  }
}
