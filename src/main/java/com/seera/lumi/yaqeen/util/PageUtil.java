package com.seera.lumi.yaqeen.util;

import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

import com.seera.lumi.yaqeen.controller.request.SearchRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

public final class PageUtil {

  private PageUtil() {}

  public static Pageable getPageable(SearchRequest searchReq) {
    int pageNumber = searchReq.getPageNumber();
    Sort.Direction direction =
        nonNull(searchReq.getOrder()) && searchReq.getOrder().equalsIgnoreCase("asc")
            ? Sort.Direction.ASC
            : Sort.Direction.DESC;
    return PageRequest.of(
        pageNumber,
        searchReq.getPageSize(),
        Sort.by(direction, isNotEmpty(searchReq.getSort()) ? "id" : searchReq.getSort()[0]));
  }
}
