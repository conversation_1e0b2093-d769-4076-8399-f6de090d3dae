package com.seera.lumi.yaqeen.util;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class DateUtil {

  private static final ZoneId ZONE = ZoneId.of("Asia/Dubai");

  public static String getCurrentDateTimeInString() {
    LocalDateTime now = LocalDateTime.ofInstant(Instant.now(), ZONE);
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
    return now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
  }

  public static LocalDateTime getCurrentDateTime() {
    return LocalDateTime.ofInstant(Instant.now(), ZONE);
  }

  public static LocalDateTime convertDate(String dateString, String pattern) {
    if (StringUtils.isEmpty(dateString) || "00000000".equals(dateString)) {
      return null;
    }
    return LocalDateTime.parse(dateString, DateTimeFormatter.BASIC_ISO_DATE.ofPattern(pattern));
  }

  public static LocalDateTime convertDateWithoutTime(String dateString, String pattern) {
    if (StringUtils.isEmpty(dateString) || "00000000".equals(dateString)) {
      return null;
    }
    return LocalDate.parse(dateString, DateTimeFormatter.ofPattern(pattern)).atStartOfDay();
  }
}
