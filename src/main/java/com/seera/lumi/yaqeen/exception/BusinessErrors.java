package com.seera.lumi.yaqeen.exception;

import org.springframework.http.HttpStatus;

public class BusinessErrors extends BaseError {
  public static final BaseError CUSTOMER_ACCOUNT_NOT_FOUND =
      new BusinessErrors(2001, "error.customer.account.not.found", HttpStatus.NOT_FOUND);
  public static final BaseError JOB_CONFIGURATION_NOT_FOUND =
      new BusinessErrors(2001, "error.job.config.not.found", HttpStatus.NOT_FOUND);
  public static final BaseError INVALID_JOB_CONFIGURATION =
      new BusinessErrors(2001, "error.job.config.invalid", HttpStatus.BAD_REQUEST);
  public static final BaseError KAFKA_TOPIC_NOT_FOUND =
      new BusinessErrors(2001, "error.kafka.topic.not.found", HttpStatus.NOT_FOUND);
  public static final BaseError INVALID_KAFKA_TOPIC =
      new BusinessErrors(2001, "error.kafka.topic.invalid.found", HttpStatus.BAD_REQUEST);
  public static final BaseError CUSTOMER_NOT_FOUND =
      new BusinessErrors(2002, "error.customer.not.found", HttpStatus.NOT_FOUND);
  public static final BaseError CUSTOMER_ID_NOT_FOUND =
      new BusinessErrors(2003, "error.customer.id.not.found", HttpStatus.BAD_REQUEST);
  public static final BaseError TAX_OFFICE_NO_REQUIRED =
      new BusinessErrors(2004, "error.vat.number.required", HttpStatus.BAD_REQUEST);

  protected BusinessErrors(int code, String desc, HttpStatus httpStatus) {
    super(code, desc, httpStatus);
  }
}
