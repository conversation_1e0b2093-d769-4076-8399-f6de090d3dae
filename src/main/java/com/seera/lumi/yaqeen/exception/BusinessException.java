package com.seera.lumi.yaqeen.exception;

public class BusinessException extends RuntimeException {

  private static final long serialVersionUID = 1905122041950251207L;

  private final BaseError baseError;

  public BusinessException(Throwable cause, BaseError baseError) {
    super(baseError.getDesc(), cause);
    this.baseError = baseError;
  }

  public BusinessException(BaseError baseError) {
    super(baseError.getDesc());
    this.baseError = baseError;
  }

  public BaseError getError() {
    return this.baseError;
  }
}
