package com.seera.lumi.yaqeen.exception;

import static com.seera.lumi.yaqeen.exception.BaseError.BAD_REQUEST;
import static com.seera.lumi.yaqeen.exception.BaseError.HTTP_METHOD_NOT_SUPPORT;
import static com.seera.lumi.yaqeen.exception.BaseError.MALFORMED_BODY;
import static com.seera.lumi.yaqeen.exception.BaseError.UNSUPPORTED_MEDIA_TYPE;
import static com.seera.lumi.yaqeen.util.LogUtil.getTraceId;

import io.micrometer.tracing.Tracer;
import java.nio.file.AccessDeniedException;
import java.util.Objects;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.TypeMismatchException;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@Order(Ordered.HIGHEST_PRECEDENCE)
@RestControllerAdvice
@RequiredArgsConstructor
@Slf4j
public class RestExceptionHandler extends ResponseEntityExceptionHandler {

  private final Tracer tracer;

  @Override
  @NonNull
  protected ResponseEntity<Object> handleHttpMessageNotReadable(
      HttpMessageNotReadableException ex,
      HttpHeaders headers,
      HttpStatusCode status,
      WebRequest request) {
    LumiCoreErrResponseMdl lumiCoreErrResponseMdl = MALFORMED_BODY.httpResponseMdl();
    lumiCoreErrResponseMdl.setReqId(getTraceId(tracer));
    return new ResponseEntity<>(lumiCoreErrResponseMdl, MALFORMED_BODY.getHttpStatus());
  }

  @Override
  @NonNull
  protected ResponseEntity<Object> handleHttpRequestMethodNotSupported(
      HttpRequestMethodNotSupportedException ex,
      HttpHeaders headers,
      HttpStatusCode status,
      WebRequest request) {
    LumiCoreErrResponseMdl lumiCoreErrResponseMdl = HTTP_METHOD_NOT_SUPPORT.httpResponseMdl();
    lumiCoreErrResponseMdl.setReqId(getTraceId(tracer));
    return new ResponseEntity<>(lumiCoreErrResponseMdl, HTTP_METHOD_NOT_SUPPORT.getHttpStatus());
  }

  @Override
  @NonNull
  protected ResponseEntity<Object> handleHttpMediaTypeNotSupported(
      HttpMediaTypeNotSupportedException ex,
      HttpHeaders headers,
      HttpStatusCode status,
      WebRequest request) {
    LumiCoreErrResponseMdl lumiCoreErrResponseMdl = UNSUPPORTED_MEDIA_TYPE.httpResponseMdl();
    lumiCoreErrResponseMdl.setReqId(getTraceId(tracer));
    return new ResponseEntity<>(lumiCoreErrResponseMdl, UNSUPPORTED_MEDIA_TYPE.getHttpStatus());
  }

  @Override
  @NonNull
  protected ResponseEntity<Object> handleMethodArgumentNotValid(
      MethodArgumentNotValidException ex,
      HttpHeaders headers,
      HttpStatusCode status,
      WebRequest request) {
    try {
      String code =
          ((DefaultMessageSourceResolvable)
                  Objects.requireNonNull(
                      ex.getBindingResult().getAllErrors().get(0).getArguments())[0])
              .getCode();
      String defaultMessage = ex.getBindingResult().getFieldErrors().get(0).getDefaultMessage();
      String errorMessage = String.format("%s %s", code, defaultMessage);
      LumiCoreErrResponseMdl lumiCoreErrResponseMdl =
          BaseError.validationError(errorMessage).httpResponseMdl();
      lumiCoreErrResponseMdl.setReqId(getTraceId(tracer));
      return new ResponseEntity<>(lumiCoreErrResponseMdl, BAD_REQUEST.getHttpStatus());
    } catch (Exception gex) {
      return new ResponseEntity<>(BAD_REQUEST.httpResponseMdl(), BAD_REQUEST.getHttpStatus());
    }
  }

  @Override
  public ResponseEntity<Object> handleTypeMismatch(
      TypeMismatchException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
    LumiCoreErrResponseMdl lumiUltraErrResponseMdl = BAD_REQUEST.httpResponseMdl();
    lumiUltraErrResponseMdl.setReqId(getTraceId(tracer));
    return new ResponseEntity<>(lumiUltraErrResponseMdl, BAD_REQUEST.getHttpStatus());
  }

  @ExceptionHandler(value = AccessDeniedException.class)
  public ResponseEntity<Object> handleAccessDeniedException(
      AccessDeniedException ex, WebRequest request) {
    BaseError baseError = BaseError.UNAUTHORIZED;
    return new ResponseEntity<>(baseError, baseError.getHttpStatus());
  }

  @ExceptionHandler({RuntimeException.class, Exception.class})
  public ResponseEntity<Object> handleAll(Exception ex, WebRequest request) {
    log.error("Internal Server Error", ex);
    BaseError baseError = BaseError.INTERNAL_SERVER_BASE_ERROR;
    return new ResponseEntity<>(baseError, baseError.getHttpStatus());
  }

  @ExceptionHandler({BusinessException.class})
  public ResponseEntity<Object> handleAll(BusinessException ex, WebRequest request) {
    BaseError baseError = ex.getError();
    return new ResponseEntity<>(baseError, baseError.getHttpStatus());
  }

  @Override
  public ResponseEntity<Object> handleMaxUploadSizeExceededException(
      MaxUploadSizeExceededException ex,
      HttpHeaders headers,
      HttpStatusCode status,
      WebRequest request) {
    log.error("Exception", ex);
    BaseError baseError = BaseError.MAX_UPLOAD_SIZE_EXCEEDED;
    return new ResponseEntity<>(baseError, baseError.getHttpStatus());
  }
}
