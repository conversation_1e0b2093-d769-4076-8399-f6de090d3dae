package com.seera.lumi.yaqeen.exception;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.http.HttpStatus;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseError {
  public static final BaseError INTERNAL_SERVER_BASE_ERROR =
      new BaseError(1001, "error.internal.server.error", HttpStatus.INTERNAL_SERVER_ERROR);
  public static final BaseError BAD_REQUEST =
      new BaseError(1002, "error.bad.request", HttpStatus.BAD_REQUEST);
  public static final BaseError UNAUTHORIZED =
      new BaseError(1003, "error.unauthorized", HttpStatus.UNAUTHORIZED);
  public static final BaseError MAX_UPLOAD_SIZE_EXCEEDED =
      new BaseError(1004, "error.max.upload.size.exceeded", HttpStatus.BAD_REQUEST);
  public static final BaseError RESOURCE_NOT_FOUND =
      new BaseError(1005, "error.resource.not.found", HttpStatus.NOT_FOUND);
  public static final BaseError ACCESS_DENIED =
      new BaseError(1006, "error.access.denied", HttpStatus.FORBIDDEN);
  public static final BaseError MALFORMED_BODY =
      new BaseError(1007, "Malformed Request Body", HttpStatus.BAD_REQUEST);
  public static final BaseError HTTP_METHOD_NOT_SUPPORT =
      new BaseError(1008, "Http Method Not Supported", HttpStatus.BAD_REQUEST);
  public static final BaseError UNSUPPORTED_MEDIA_TYPE =
      new BaseError(1009, "Unsupported MediaType", HttpStatus.BAD_REQUEST);

  protected String code;
  protected String desc;
  protected String reqId;
  protected String path;
  protected String st;

  @JsonIgnore private HttpStatus httpStatus;

  protected BaseError(int code, String desc, HttpStatus httpStatus) {
    this.code = "YQB" + code;
    this.desc = desc;
    this.httpStatus = httpStatus;
  }

  public static BaseError validationError(String msg) {
    return new BaseError(1002, msg, HttpStatus.BAD_REQUEST);
  }

  public LumiCoreErrResponseMdl httpResponseMdl() {
    LumiCoreErrResponseMdl lumiCoreErrResponseMdl = new LumiCoreErrResponseMdl();
    lumiCoreErrResponseMdl.setCode(this.code);
    lumiCoreErrResponseMdl.setDesc(this.desc);
    return lumiCoreErrResponseMdl;
  }
}
