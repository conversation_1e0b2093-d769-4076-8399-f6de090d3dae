package com.seera.lumi.yaqeen.ai.service;

import com.seera.lumi.yaqeen.ai.model.AIModel;
import com.seera.lumi.yaqeen.ai.registry.AIModelRegistry;
import com.seera.lumi.yaqeen.ai.response.BaseResponse;
import com.seera.lumi.yaqeen.ai.selector.AIModelSelector;
import com.seera.lumi.yaqeen.ai.tracker.AIModelUsageTracker;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
@RequiredArgsConstructor
public class AIService {
  private final AIModelSelector modelSelector;
  private final AIModelUsageTracker usageTracker;
  private final AIModelRegistry modelRegistry;
  private final RestTemplate restTemplate;

  private final String geminiApiKey = "AIzaSyCX0ReBG_jyXVXVaWBuj9Pn-kbXj_F4ecw";
  private final String groqApiKey = "********************************************************";

  public BaseResponse generateContent(String prompt, Optional<AIModel> modelOpt) {
    if (modelOpt.isEmpty()) {
      throw new RuntimeException("No available AI models found");
    }
    while (modelOpt.isPresent()) {
      AIModel model = modelOpt.get();
      try {
        AIProvider provider = createProvider(model);
        return provider.generateContent(prompt);
      } catch (Exception e) {
        log.error("Error with model {}: {}", model.getModelId(), e.getMessage());
        modelOpt = modelSelector.selectBestModel();
      }
    }

    throw new RuntimeException("No available models after retries");
  }

  private AIProvider createProvider(AIModel model) {
    if (model.getModelId().startsWith("gemini")) {
      return new GeminiProvider(model, usageTracker, modelRegistry, restTemplate, geminiApiKey);
    } else if (model.getModelId().startsWith("meta-llama")) {
      return new GroqProvider(model, usageTracker, modelRegistry, restTemplate, groqApiKey);
    } else {
      throw new RuntimeException("Unsupported model type: " + model.getModelId());
    }
  }
}
