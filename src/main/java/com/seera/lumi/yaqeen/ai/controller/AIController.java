package com.seera.lumi.yaqeen.ai.controller;

import com.seera.lumi.yaqeen.ai.model.AIModel;
import com.seera.lumi.yaqeen.ai.response.BaseResponse;
import com.seera.lumi.yaqeen.ai.selector.AIModelSelector;
import com.seera.lumi.yaqeen.ai.service.AIService;
import com.seera.lumi.yaqeen.ai.tracker.AIModelUsageTracker;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/ai")
@RequiredArgsConstructor
public class AIController {

  private final AIService aiService;
  private final AIModelSelector modelSelector;
  private final AIModelUsageTracker usageTracker;

  @GetMapping("/generate")
  public ResponseEntity<BaseResponse> generateContent(
      @RequestParam(name = "modelId", required = false) String modelId,
      @RequestParam String prompt) {
    log.info("Received request to generate content with prompt: {}", prompt);
    try {
      Optional<AIModel> modelOpt;
      if (StringUtils.isNotBlank(modelId)) {
        modelOpt = modelSelector.selectModelById(modelId);
      } else {
        modelOpt = modelSelector.selectBestModel();
      }
      return ResponseEntity.ok(aiService.generateContent(prompt, modelOpt));
    } catch (Exception e) {
      log.error("Error generating content: {}", e.getMessage());
      return ResponseEntity.internalServerError()
          .body(
              BaseResponse.builder()
                  .message("Error generating content: " + e.getMessage())
                  .build());
    }
  }

  @GetMapping("/models")
  public ResponseEntity<BaseResponse> getAllModels() {
    log.info("Received request to get all models");
    try {
      List<AIModel> models = modelSelector.getAllModels();
      return ResponseEntity.ok(
          BaseResponse.builder().message("Successfully retrieved all models").data(models).build());
    } catch (Exception e) {
      log.error("Error getting models: {}", e.getMessage());
      return ResponseEntity.internalServerError()
          .body(BaseResponse.builder().message("Error getting models: " + e.getMessage()).build());
    }
  }

  @GetMapping("/models/best")
  public ResponseEntity<BaseResponse> getBestModel() {
    log.info("Received request to get best model");
    try {
      AIModel bestModel =
          modelSelector
              .selectBestModel()
              .orElseThrow(() -> new RuntimeException("No available AI models found"));
      return ResponseEntity.ok(
          BaseResponse.builder()
              .message("Successfully retrieved best model")
              .data(bestModel)
              .build());
    } catch (Exception e) {
      log.error("Error getting best model: {}", e.getMessage());
      return ResponseEntity.internalServerError()
          .body(
              BaseResponse.builder()
                  .message("Error getting best model: " + e.getMessage())
                  .build());
    }
  }

  @GetMapping("/models/usage")
  public ResponseEntity<BaseResponse> getModelUsage() {
    log.info("Received request to get model usage statistics");
    try {
      Map<String, Map<String, Integer>> usageStats = usageTracker.getUsageStatistics();
      return ResponseEntity.ok(
          BaseResponse.builder()
              .message("Successfully retrieved model usage statistics")
              .data(usageStats)
              .build());
    } catch (Exception e) {
      log.error("Error getting model usage statistics: {}", e.getMessage());
      return ResponseEntity.internalServerError()
          .body(
              BaseResponse.builder()
                  .message("Error getting model usage statistics: " + e.getMessage())
                  .build());
    }
  }

  @GetMapping("/models/{modelId}/usage")
  public ResponseEntity<BaseResponse> getModelUsageById(@PathVariable String modelId) {
    log.info("Received request to get usage statistics for model: {}", modelId);
    try {
      Map<String, Integer> modelStats = usageTracker.getModelUsageStatistics(modelId);
      return ResponseEntity.ok(
          BaseResponse.builder()
              .message("Successfully retrieved usage statistics for model: " + modelId)
              .data(modelStats)
              .build());
    } catch (Exception e) {
      log.error("Error getting usage statistics for model {}: {}", modelId, e.getMessage());
      return ResponseEntity.internalServerError()
          .body(
              BaseResponse.builder()
                  .message("Error getting usage statistics: " + e.getMessage())
                  .build());
    }
  }
}
