package com.seera.lumi.yaqeen.ai.tracker;

import com.seera.lumi.yaqeen.ai.model.AIModel;
import com.seera.lumi.yaqeen.ai.registry.AIModelRegistry;
import com.seera.lumi.yaqeen.service.CacheService;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AIModelUsageTracker {
  private static final String REQUESTS_PER_MINUTE_KEY = "model:%s:requests_per_minute:%s";
  private static final String REQUESTS_PER_DAY_KEY = "model:%s:requests_per_day:%s";
  private static final String TOTAL_REQUESTS_KEY = "model:%s:total_requests";
  private static final String FAILED_REQUESTS_KEY = "model:%s:failed_requests";
  private static final long CACHE_TTL_MS = 1000; // 1 second cache TTL
  private final AIModelRegistry modelRegistry;
  private final CacheService cacheService;
  // Cache window counts to reduce Redis calls
  private final Map<String, WindowCount> windowCountCache = new HashMap<>();

  public void trackRequest(String modelId, boolean success) {
    long currentTime = System.currentTimeMillis();
    String minuteKey = String.format(REQUESTS_PER_MINUTE_KEY, modelId, currentTime);
    String dayKey = String.format(REQUESTS_PER_DAY_KEY, modelId, currentTime);
    String totalKey = String.format(TOTAL_REQUESTS_KEY, modelId);

    // Increment counters with TTL
    incrementCounter(minuteKey, 1, TimeUnit.MINUTES);
    incrementCounter(dayKey, 24, TimeUnit.HOURS);
    incrementCounter(totalKey, 30, TimeUnit.DAYS);

    if (!success) {
      String failedKey = String.format(FAILED_REQUESTS_KEY, modelId);
      incrementCounter(failedKey, 30, TimeUnit.DAYS);
    }

    // Invalidate cache for this model
    windowCountCache.remove(modelId);
  }

  private void incrementCounter(String key, long ttl, TimeUnit unit) {
    Integer currentValue = cacheService.get(key, Integer.class);
    if (currentValue == null) {
      currentValue = 0;
    }
    cacheService.put(key, currentValue + 1, ttl, unit);
  }

  public boolean isWithinLimits(AIModel model) {
    String modelId = model.getModelId();
    long currentTime = System.currentTimeMillis();

    // Check cache first
    WindowCount cachedCount = windowCountCache.get(modelId);
    if (cachedCount != null && (currentTime - cachedCount.timestamp) < CACHE_TTL_MS) {
      return cachedCount.minuteCount < model.getRequestsPerMinute()
          && cachedCount.dayCount < model.getRequestsPerDay();
    }

    // If cache miss or expired, calculate new counts
    int minuteCount = getMinuteWindowCount(modelId, currentTime);
    int dayCount = getDayWindowCount(modelId, currentTime);

    // Update cache
    windowCountCache.put(modelId, new WindowCount(currentTime, minuteCount, dayCount));

    return minuteCount < model.getRequestsPerMinute() && dayCount < model.getRequestsPerDay();
  }

  private int getMinuteWindowCount(String modelId, long currentTime) {
    long windowStart = currentTime - TimeUnit.MINUTES.toMillis(1);
    // Only check every 5 seconds to reduce Redis calls
    int count = 0;
    for (long time = windowStart; time <= currentTime; time += 5000) {
      String key = String.format(REQUESTS_PER_MINUTE_KEY, modelId, time);
      Integer value = cacheService.get(key, Integer.class);
      if (value != null) {
        count += value;
      }
    }
    return count;
  }

  private int getDayWindowCount(String modelId, long currentTime) {
    long windowStart = currentTime - TimeUnit.HOURS.toMillis(24);
    // Only check every 5 minutes to reduce Redis calls
    int count = 0;
    for (long time = windowStart; time <= currentTime; time += TimeUnit.MINUTES.toMillis(5)) {
      String key = String.format(REQUESTS_PER_DAY_KEY, modelId, time);
      Integer value = cacheService.get(key, Integer.class);
      if (value != null) {
        count += value;
      }
    }
    return count;
  }

  public double getSuccessRate(String modelId) {
    String totalKey = String.format(TOTAL_REQUESTS_KEY, modelId);
    String failedKey = String.format(FAILED_REQUESTS_KEY, modelId);

    Integer total = cacheService.get(totalKey, Integer.class);
    Integer failed = cacheService.get(failedKey, Integer.class);

    total = total != null ? total : 0;
    failed = failed != null ? failed : 0;

    return total == 0 ? 0.0 : (double) (total - failed) / total;
  }

  public Map<String, Map<String, Integer>> getUsageStatistics() {
    Map<String, Map<String, Integer>> stats = new HashMap<>();
    modelRegistry
        .getAllModels()
        .forEach(
            model -> {
              String modelId = model.getModelId();
              stats.put(modelId, getModelUsageStatistics(modelId));
            });
    return stats;
  }

  public Map<String, Integer> getModelUsageStatistics(String modelId) {
    Map<String, Integer> stats = new HashMap<>();
    long currentTime = System.currentTimeMillis();

    int minuteCount = getMinuteWindowCount(modelId, currentTime);
    int dayCount = getDayWindowCount(modelId, currentTime);

    String totalKey = String.format(TOTAL_REQUESTS_KEY, modelId);
    String failedKey = String.format(FAILED_REQUESTS_KEY, modelId);

    Integer totalValue = cacheService.get(totalKey, Integer.class);
    Integer failedValue = cacheService.get(failedKey, Integer.class);

    stats.put("requestsPerMinute", minuteCount);
    stats.put("requestsPerDay", dayCount);
    stats.put("totalRequests", totalValue != null ? totalValue : 0);
    stats.put("failedRequests", failedValue != null ? failedValue : 0);
    stats.put("successRate", (int) (getSuccessRate(modelId) * 100));

    return stats;
  }

  private static class WindowCount {
    final long timestamp;
    final int minuteCount;
    final int dayCount;

    WindowCount(long timestamp, int minuteCount, int dayCount) {
      this.timestamp = timestamp;
      this.minuteCount = minuteCount;
      this.dayCount = dayCount;
    }
  }
}
