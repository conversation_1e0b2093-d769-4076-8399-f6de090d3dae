package com.seera.lumi.yaqeen.ai.config;

import com.seera.lumi.yaqeen.ai.model.AIModel;
import com.seera.lumi.yaqeen.ai.registry.AIModelRegistry;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@Configuration
@EnableScheduling
@RequiredArgsConstructor
public class AIConfig {

  private final AIModelRegistry modelRegistry;

  @PostConstruct
  public void initializeModels() {
    // modelRegistry.registerModel(
    //     AIModel.builder()
    //         .modelId("gemini-2.5-pro-exp-03-25")
    //         .modelName("gemini-2.5-pro-exp-03-25")
    //         .requestsPerMinute(5)
    //         .requestsPerDay(25)
    //         .isActive(true)
    //         .build());

    // modelRegistry.registerModel(
    //     AIModel.builder()
    //         .modelId("gemini-2.5-flash-preview-04-17")
    //         .modelName("gemini-2.5-flash-preview-04-17")
    //         .requestsPerMinute(10)
    //         .requestsPerDay(500)
    //         .isActive(true)
    //         .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("gemini-2.0-flash")
            .modelName("gemini-2.0-flash")
            .requestsPerMinute(15)
            .requestsPerDay(1500)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("gemini-2.0-flash-exp-image-generation")
            .modelName("gemini-2.0-flash-exp-image-generation")
            .requestsPerMinute(15)
            .requestsPerDay(1500)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("gemini-2.0-flash-lite")
            .modelName("gemini-2.0-flash-lite")
            .requestsPerMinute(30)
            .requestsPerDay(1500)
            .isActive(true)
            .build());

    // Add free Gemini model
    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("gemini-1.5-pro")
            .modelName("gemini-1.5-pro")
            .requestsPerMinute(2)
            .requestsPerDay(50)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("gemini-1.5-flash")
            .modelName("gemini-1.5-flash")
            .requestsPerMinute(15)
            .requestsPerDay(1500)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("gemini-1.5-flash-8b")
            .modelName("gemini-1.5-flash-8b")
            .requestsPerMinute(15)
            .requestsPerDay(1500)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("allam-2-7b")
            .modelName("allam-2-7b")
            .requestsPerMinute(30)
            .requestsPerDay(7000)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("compound-beta")
            .modelName("compound-beta")
            .requestsPerMinute(15)
            .requestsPerDay(200)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("compound-beta-mini")
            .modelName("compound-beta-mini")
            .requestsPerMinute(15)
            .requestsPerDay(200)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("deepseek-r1-distill-llama-70b")
            .modelName("deepseek-r1-distill-llama-70b")
            .requestsPerMinute(30)
            .requestsPerDay(1000)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("distil-whisper-large-v3-en")
            .modelName("distil-whisper-large-v3-en")
            .requestsPerMinute(20)
            .requestsPerDay(2000)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("gemma2-9b-it")
            .modelName("gemma2-9b-it")
            .requestsPerMinute(30)
            .requestsPerDay(14400)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("llama-3.1-8b-instant")
            .modelName("llama-3.1-8b-instant")
            .requestsPerMinute(30)
            .requestsPerDay(14400)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("llama-3.3-70b-versatile")
            .modelName("llama-3.3-70b-versatile")
            .requestsPerMinute(30)
            .requestsPerDay(1000)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("llama-guard-3-8b")
            .modelName("llama-guard-3-8b")
            .requestsPerMinute(30)
            .requestsPerDay(14400)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("llama3-70b-8192")
            .modelName("llama3-70b-8192")
            .requestsPerMinute(30)
            .requestsPerDay(14400)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("llama3-8b-8192")
            .modelName("llama3-8b-8192")
            .requestsPerMinute(30)
            .requestsPerDay(14400)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("meta-llama/llama-4-maverick-17b-128e-instruct")
            .modelName("meta-llama/llama-4-maverick-17b-128e-instruct")
            .requestsPerMinute(30)
            .requestsPerDay(1000)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("meta-llama/llama-4-scout-17b-16e-instruct")
            .modelName("meta-llama/llama-4-scout-17b-16e-instruct")
            .requestsPerMinute(30)
            .requestsPerDay(1000)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("meta-llama/llama-guard-4-12b")
            .modelName("meta-llama/llama-guard-4-12b")
            .requestsPerMinute(30)
            .requestsPerDay(14400)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("mistral-saba-24b")
            .modelName("mistral-saba-24b")
            .requestsPerMinute(30)
            .requestsPerDay(1000)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("playai-tts")
            .modelName("playai-tts")
            .requestsPerMinute(10)
            .requestsPerDay(100)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("playai-tts-arabic")
            .modelName("playai-tts-arabic")
            .requestsPerMinute(10)
            .requestsPerDay(100)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("qwen-qwq-32b")
            .modelName("qwen-qwq-32b")
            .requestsPerMinute(30)
            .requestsPerDay(1000)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("whisper-large-v3")
            .modelName("whisper-large-v3")
            .requestsPerMinute(20)
            .requestsPerDay(2000)
            .isActive(true)
            .build());

    modelRegistry.registerModel(
        AIModel.builder()
            .modelId("whisper-large-v3-turbo")
            .modelName("whisper-large-v3-turbo")
            .requestsPerMinute(20)
            .requestsPerDay(2000)
            .isActive(true)
            .build());
  }

  @Scheduled(fixedRate = 60000) // Reset every minute
  public void resetMinuteCounters() {
    modelRegistry
        .getAllModels()
        .forEach(model -> modelRegistry.updateModel(model.toBuilder().totalRequests(0).build()));
  }

  @Scheduled(cron = "0 0 0 * * ?") // Reset at midnight
  public void resetDayCounters() {
    modelRegistry
        .getAllModels()
        .forEach(model -> modelRegistry.updateModel(model.toBuilder().totalRequests(0).build()));
  }
}
