package com.seera.lumi.yaqeen.ai.registry;

import com.seera.lumi.yaqeen.ai.model.AIModel;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
public class AIModelRegistry {
  private final Map<String, AIModel> models = Collections.synchronizedMap(new LinkedHashMap<>());

  public void registerModel(AIModel model) {
    models.put(model.getModelId(), model);
  }

  public AIModel getModel(String modelId) {
    return models.get(modelId);
  }

  public List<AIModel> getAllModels() {
    return new ArrayList<>(models.values());
  }

  public void updateModel(AIModel model) {
    models.put(model.getModelId(), model);
  }

  public void removeModel(String modelId) {
    models.remove(modelId);
  }
}
