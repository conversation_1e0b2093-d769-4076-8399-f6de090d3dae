package com.seera.lumi.yaqeen.ai.selector;

import com.seera.lumi.yaqeen.ai.model.AIModel;
import com.seera.lumi.yaqeen.ai.registry.AIModelRegistry;
import com.seera.lumi.yaqeen.ai.tracker.AIModelUsageTracker;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Component;

@Component
public class AIModelSelector {
  private final AIModelRegistry modelRegistry;
  private final AIModelUsageTracker usageTracker;

  public AIModelSelector(AIModelRegistry modelRegistry, AIModelUsageTracker usageTracker) {
    this.modelRegistry = modelRegistry;
    this.usageTracker = usageTracker;
  }

  public List<AIModel> getAllModels() {
    return modelRegistry.getAllModels();
  }

  public Optional<AIModel> selectBestModel() {
    return modelRegistry.getAllModels().stream()
        .filter(model -> model.isActive() && usageTracker.isWithinLimits(model))
        .max(
            Comparator.comparingDouble(
                model -> {
                  double successRate = usageTracker.getSuccessRate(model.getModelId());
                  return successRate
                      * (1.0 - (double) model.getTotalRequests() / model.getRequestsPerDay());
                }));
  }

  public Optional<AIModel> selectModelById(String modelId) {
    AIModel model = modelRegistry.getModel(modelId);
    if (model != null && model.isActive() && usageTracker.isWithinLimits(model)) {
      return Optional.of(model);
    }
    return Optional.empty();
  }
}
