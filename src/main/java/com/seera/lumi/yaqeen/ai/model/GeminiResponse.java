package com.seera.lumi.yaqeen.ai.model;

import lombok.Data;
import java.util.List;

@Data
public class GeminiResponse {
  private List<Candidate> candidates;
  private UsageMetadata usageMetadata;
  private String modelVersion;

  @Data
  public static class Candidate {
    private Content content;
    private String finishReason;
    private double avgLogprobs;
  }

  @Data
  public static class Content {
    private List<Part> parts;
    private String role;
  }

  @Data
  public static class Part {
    private String text;
  }

  @Data
  public static class UsageMetadata {
    private int promptTokenCount;
    private int candidatesTokenCount;
    private int totalTokenCount;
    private List<TokenDetail> promptTokensDetails;
    private List<TokenDetail> candidatesTokensDetails;
  }

  @Data
  public static class TokenDetail {
    private String modality;
    private int tokenCount;
  }
}
