package com.seera.lumi.yaqeen.ai.model;

import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class AIModel {
  private String modelId;
  private String modelName;
  private int requestsPerMinute;
  private int requestsPerDay;
  private String apiKey;
  private String endpoint;
  @Builder.Default private boolean isActive = true;
  @Builder.Default private double successRate = 1.0;
  @Builder.Default private long totalRequests = 0;
  @Builder.Default private long failedRequests = 0;
}
