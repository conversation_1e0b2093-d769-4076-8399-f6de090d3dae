package com.seera.lumi.yaqeen.ai.service;

import com.seera.lumi.yaqeen.ai.model.AIModel;
import com.seera.lumi.yaqeen.ai.registry.AIModelRegistry;
import com.seera.lumi.yaqeen.ai.response.BaseResponse;
import com.seera.lumi.yaqeen.ai.tracker.AIModelUsageTracker;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

@Slf4j
@RequiredArgsConstructor
public class GroqProvider implements AIProvider {
  private final AIModel model;
  private final AIModelUsageTracker usageTracker;
  private final AIModelRegistry modelRegistry;
  private final RestTemplate restTemplate;
  private final String apiKey;

  @Override
  public BaseResponse generateContent(String prompt) {
    try {
      log.info("Using Groq model: {} for prompt", model.getModelId());

      String url = "https://api.groq.com/openai/v1/chat/completions";

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      headers.setBearerAuth(apiKey);

      Map<String, Object> message = new HashMap<>();
      message.put("role", "user");
      message.put("content", prompt);

      List<Map<String, Object>> messages = new ArrayList<>();
      messages.add(message);

      Map<String, Object> requestBody = new HashMap<>();
      requestBody.put("model", model.getModelId());
      requestBody.put("messages", messages);

      HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
      Map<String, Object> response = restTemplate.postForObject(url, request, Map.class);

      usageTracker.trackRequest(model.getModelId(), true);

      if (response == null || !response.containsKey("choices")) {
        throw new RuntimeException("No response received from Groq model");
      }

      List<Map<String, Object>> choices = (List<Map<String, Object>>) response.get("choices");
      Map<String, Object> firstChoice = choices.get(0);
      Map<String, Object> messageObj = (Map<String, Object>) firstChoice.get("message");
      String content = (String) messageObj.get("content");
      log.info("Response from Groq model: {}", content);
      return BaseResponse.builder().message(content).modelVersion(model.getModelId()).build();
    } catch (HttpStatusCodeException e) {
      if (e.getStatusCode() == HttpStatus.TOO_MANY_REQUESTS) {
        log.warn(
            "Rate limit exceeded for Groq model: {}. Deactivating for 1 minute.",
            model.getModelId());
        modelRegistry.updateModel(model.toBuilder().isActive(false).build());
        throw new RuntimeException(
            "Rate limit exceeded for Groq model. Please try again in 1 minute.");
      }
      usageTracker.trackRequest(model.getModelId(), false);
      log.error(
          "Error generating content with Groq model {}: {}", model.getModelId(), e.getMessage());
      throw new RuntimeException("Failed to generate content with Groq: " + e.getMessage(), e);
    } catch (Exception e) {
      usageTracker.trackRequest(model.getModelId(), false);
      log.error(
          "Error generating content with Groq model {}: {}", model.getModelId(), e.getMessage());
      throw new RuntimeException("Failed to generate content with Groq: " + e.getMessage(), e);
    }
  }

  @Override
  public String getProviderName() {
    return "GROQ";
  }
}
