package com.seera.lumi.yaqeen.ai.service;

import com.seera.lumi.yaqeen.ai.model.AIModel;
import com.seera.lumi.yaqeen.ai.model.GeminiResponse;
import com.seera.lumi.yaqeen.ai.registry.AIModelRegistry;
import com.seera.lumi.yaqeen.ai.response.BaseResponse;
import com.seera.lumi.yaqeen.ai.tracker.AIModelUsageTracker;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

@Slf4j
@RequiredArgsConstructor
public class GeminiProvider implements AIProvider {
  private final AIModel model;
  private final AIModelUsageTracker usageTracker;
  private final AIModelRegistry modelRegistry;
  private final RestTemplate restTemplate;
  private final String apiKey;

  @Override
  public BaseResponse generateContent(String prompt) {
    try {
      log.info("Using Gemini model: {} for prompt", model.getModelId());

      String url =
          String.format(
              "https://generativelanguage.googleapis.com/v1beta/models/%s:generateContent?key=%s",
              model.getModelId(), apiKey);

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);

      Map<String, Object> requestBody = new HashMap<>();
      Map<String, Object> content = new HashMap<>();
      content.put("parts", new Object[] {Map.of("text", prompt)});
      requestBody.put("contents", new Object[] {content});

      HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
      GeminiResponse response = restTemplate.postForObject(url, request, GeminiResponse.class);

      usageTracker.trackRequest(model.getModelId(), true);

      if (response == null
          || response.getCandidates() == null
          || response.getCandidates().isEmpty()) {
        throw new RuntimeException("No response received from Gemini model");
      }
      log.info(
          "Response from Gemini model: {}",
          response.getCandidates().get(0).getContent().getParts().get(0).getText());
      return BaseResponse.builder()
          .message(response.getCandidates().get(0).getContent().getParts().get(0).getText())
          .modelVersion(response.getModelVersion())
          .build();
    } catch (HttpStatusCodeException e) {
      if (e.getStatusCode() == HttpStatus.TOO_MANY_REQUESTS) {
        log.warn(
            "Rate limit exceeded for Gemini model: {}. Deactivating for 1 minute.",
            model.getModelId());
        modelRegistry.updateModel(model.toBuilder().isActive(false).build());
        throw new RuntimeException(
            "Rate limit exceeded for Gemini model. Please try again in 1 minute.");
      }
      usageTracker.trackRequest(model.getModelId(), false);
      log.error(
          "Error generating content with Gemini model {}: {}", model.getModelId(), e.getMessage());
      throw new RuntimeException("Failed to generate content with Gemini: " + e.getMessage(), e);
    } catch (Exception e) {
      usageTracker.trackRequest(model.getModelId(), false);
      log.error(
          "Error generating content with Gemini model {}: {}", model.getModelId(), e.getMessage());
      throw new RuntimeException("Failed to generate content with Gemini: " + e.getMessage(), e);
    }
  }

  @Override
  public String getProviderName() {
    return "GEMINI";
  }
}
