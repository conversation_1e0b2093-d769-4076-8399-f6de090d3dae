package com.seera.lumi.yaqeen.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "account_address")
public class AccountAddress implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "house_no", nullable = false)
  private String houseNo;

  @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "address_line_name_id", unique = true)
  private Multilingual fullAddress;

  @Column(name = "zip")
  private String zip;

  @Column(name = "city")
  private String city;

  @Column(name = "country")
  private String country;
}
