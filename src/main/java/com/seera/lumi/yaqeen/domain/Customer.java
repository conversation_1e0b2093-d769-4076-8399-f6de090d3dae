package com.seera.lumi.yaqeen.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(name = "corporate_customer")
@Inheritance(strategy = InheritanceType.JOINED)
public class Customer implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(unique = true)
  private Multilingual name;

  @OneToMany(fetch = FetchType.EAGER, mappedBy = "customer", cascade = CascadeType.ALL)
  private Set<CustomerAccount> customerAccounts = new HashSet<>();

  @Column(name = "vat_no", nullable = false)
  private Long vatNo;

  @Column(name = "cr_no")
  private String crNo;

  @Override
  public boolean equals(Object object) {
    if (this == object) return true;
    if (object == null || getClass() != object.getClass()) return false;
    Customer customer = (Customer) object;
    return Objects.equals(vatNo, customer.vatNo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(vatNo);
  }
}
