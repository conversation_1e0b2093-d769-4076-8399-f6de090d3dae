package com.seera.lumi.yaqeen.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Entity
@Getter
@Setter
@Table(name = "multilingual")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Multilingual extends BaseEntity {

  @Column(name = "en")
  private String en;

  @Column(name = "ar")
  private String ar;

  public Multilingual en(String en) {
    this.setEn(en);
    return this;
  }

  public Multilingual ar(String ar) {
    this.setAr(ar);
    return this;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof Multilingual)) {
      return false;
    }
    return this.getId() != null && this.getId().equals(((Multilingual) o).getId());
  }

  @Override
  public int hashCode() {
    return getClass().hashCode();
  }

  @Override
  public String toString() {
    return "Multilingual{"
        + "id="
        + getId()
        + ", en='"
        + getEn()
        + "'"
        + ", ar='"
        + getAr()
        + "'"
        + "}";
  }
}
