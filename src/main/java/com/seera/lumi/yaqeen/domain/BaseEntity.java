package com.seera.lumi.yaqeen.domain;

import jakarta.persistence.*;
import java.io.Serializable;
import lombok.Data;
import org.springframework.boot.actuate.audit.listener.AuditListener;

@Data
@MappedSuperclass
@EntityListeners(AuditListener.class)
public class BaseEntity implements Serializable {

  private static final long serialVersionUID = -4635974310968315539L;

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;
}
