package com.seera.lumi.yaqeen.domain;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Table(name = "replication_task_history")
public class ReplicationTaskHistory implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Column(name = "replication_job_id")
  private Long replicationJobId;

  @Column(name = "job_id")
  private Long jobId;

  @Column(name = "task_id")
  private Integer taskId;

  @Column(name = "fetched_record")
  private Integer fetchedRecord;

  @Column(name = "updated_record")
  private Integer updatedRecord;

  @Column(name = "start_time")
  private LocalDateTime startTime;

  @Column(name = "end_time")
  private LocalDateTime endTime;

  @Column(name = "job_status")
  private String jobStatus;

  @Column(name = "remark")
  private String remark;

  @Override
  public String toString() {
    return "ReplicationTaskHistory{"
        + "id="
        + id
        + ", replicationJobId="
        + replicationJobId
        + ", jobId="
        + jobId
        + ", taskId="
        + taskId
        + ", fetchedRecord="
        + fetchedRecord
        + ", updatedRecord="
        + updatedRecord
        + ", startTime="
        + startTime
        + ", endTime="
        + endTime
        + ", jobStatus='"
        + jobStatus
        + '\''
        + ", remark='"
        + remark
        + '\''
        + '}';
  }
}
