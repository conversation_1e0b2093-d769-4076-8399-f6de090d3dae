package com.seera.lumi.yaqeen.domain;

import com.seera.lumi.yaqeen.util.CommonUtils;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Data
@Entity
@Table(name = "audit_event")
public class AuditEvent {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "database_name")
  private String database;

  @Column(name = "table_name")
  private String table;

  @Column(name = "operation")
  private String operation;

  @Column(name = "primary_key")
  private String primaryKey;

  @Column(name = "snapshot")
  private String snapshot;

  @Column(name = "remarks")
  private String remarks;

  @Column(name = "timestamp")
  private Long timestamp;

  @Column(name = "updated_by")
  private String updatedBy;

  @Override
  public String toString() {
    return CommonUtils.getStrFromObj(this);
  }
}
