package com.seera.lumi.yaqeen.domain.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import com.seera.lumi.yaqeen.domain.ChangeDetail;

import java.util.Map;

@Converter
public class ChangeDetailMapConverter
    implements AttributeConverter<Map<String, ChangeDetail>, String> {
  private static final ObjectMapper objectMapper = new ObjectMapper();

  @Override
  public String convertToDatabaseColumn(Map<String, ChangeDetail> attribute) {
    if (attribute == null) {
      return null;
    }
    try {
      return objectMapper.writeValueAsString(attribute);
    } catch (JsonProcessingException e) {
      throw new RuntimeException("Error converting ChangeDetail Map to JSON", e);
    }
  }

  @Override
  public Map<String, ChangeDetail> convertToEntityAttribute(String dbData) {
    if (dbData == null || dbData.isEmpty()) {
      return null;
    }
    try {
      return objectMapper.readValue(dbData, new TypeReference<Map<String, ChangeDetail>>() {});
    } catch (JsonProcessingException e) {
      throw new RuntimeException("Error converting JSON to ChangeDetail Map", e);
    }
  }
}
