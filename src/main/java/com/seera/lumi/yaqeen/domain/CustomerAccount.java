package com.seera.lumi.yaqeen.domain;

import com.seera.lumi.yaqeen.enums.CustomerType;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "corporate_customer_account")
@NamedEntityGraph(
    name = "customer-account-entity-graph-with-billing-address",
    attributeNodes = {
      @NamedAttributeNode("name"),
      @NamedAttributeNode(value = "billingAddress", subgraph = "billing-address-subgraph"),
    },
    subgraphs = {
      @NamedSubgraph(
          name = "billing-address-subgraph",
          attributeNodes = {@NamedAttributeNode("fullAddress")})
    })
public class CustomerAccount implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(unique = true)
  private Multilingual name;

  @ManyToOne private Customer customer;

  @Column(name = "email")
  private String email;

  @Column(name = "phone")
  private String phone;

  @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
  private AccountAddress billingAddress;

  @Column(name = "carpro_account_id")
  private String carProId;

  @Column(name = "sap_id")
  private String sapId;

  @Column(name = "region")
  private String region;

  @Column(name = "account_type")
  private String accountType;

  @Column(name = "is_active")
  private Boolean isActive = Boolean.TRUE;

  @Column(name = "customer_type")
  @Enumerated(EnumType.STRING)
  private CustomerType customerType;

  @Column(name = "created_on")
  private LocalDateTime createdOn;

  @Override
  public boolean equals(Object object) {
    if (this == object) return true;
    if (object == null || getClass() != object.getClass()) return false;
    CustomerAccount that = (CustomerAccount) object;
    return Objects.equals(id, that.id);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id);
  }
}
