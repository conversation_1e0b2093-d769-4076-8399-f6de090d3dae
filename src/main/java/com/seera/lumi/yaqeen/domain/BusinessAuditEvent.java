package com.seera.lumi.yaqeen.domain;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Data
@Entity
@Table(name = "business_audit_events")
@EqualsAndHashCode(callSuper = true)
public class BusinessAuditEvent extends BaseEntity {

  @Column(name = "audit_id", nullable = false, unique = true)
  private String auditId;

  @Column(name = "user_id")
  private String userId;

  @Column(name = "action")
  private String action;

  @Column(name = "resource_type")
  private String resourceType;

  @Column(name = "resource_value")
  private String resourceValue;

  @Column(name = "description", columnDefinition = "TEXT")
  private String description;

  @Column(name = "method_name")
  private String methodName;

  @Column(name = "class_name")
  private String className;

  @Column(name = "parameters")
  private String parameters;

  @Column(name = "result")
  private String result;

  @Column(name = "trace_id")
  private String traceId;

  @Column(name = "client_id")
  private String clientId;

  @Column(name = "timestamp")
  private LocalDateTime timestamp;

  @Column(name = "http_method")
  private String httpMethod;

  @Column(name = "request_uri")
  private String requestUri;

  @Column(name = "user_agent")
  private String userAgent;

  @Column(name = "ip_address")
  private String ipAddress;

  @Column(name = "success")
  private Boolean success;

  @Column(name = "error_message", columnDefinition = "TEXT")
  private String errorMessage;

  @Column(name = "execution_time_ms")
  private Long executionTimeMs;

  @Column(name = "created_on", nullable = false)
  @CreationTimestamp
  private LocalDateTime createdOn;

  @Column(name = "updated_on", nullable = false)
  @UpdateTimestamp
  private LocalDateTime updatedOn;
}
