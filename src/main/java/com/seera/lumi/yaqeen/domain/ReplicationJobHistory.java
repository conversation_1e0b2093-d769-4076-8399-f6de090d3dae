package com.seera.lumi.yaqeen.domain;

import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Table(name = "replication_job_history")
public class ReplicationJobHistory implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Column(name = "job_id")
  private Integer jobId;

  @Column(name = "total_task")
  private Integer totalTask;

  @Column(name = "total_record")
  private Long totalRecord;

  @Column(name = "start_time")
  private LocalDateTime startTime;
}
