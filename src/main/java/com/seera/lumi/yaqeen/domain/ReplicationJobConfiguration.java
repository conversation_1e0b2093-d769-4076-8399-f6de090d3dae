package com.seera.lumi.yaqeen.domain;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Type;

@Getter
@Setter
@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Table(name = "replication_job_config")
public class ReplicationJobConfiguration implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "job_id")
  // jobid is just a unique id for job config
  private Integer jobId;

  // job name is used to pass as a argument in clockwork job and carpro api
  @Column(name = "job_name")
  private String jobName;

  // just a description of a job
  @Column(name = "job_description")
  private String jobDescription;

  // source name of a job like carpro or petromin
  @Column(name = "source_name")
  private String sourceName;

  // mention all the column we need to fetch from database
  @Lob
  @Column(name = "columns", columnDefinition = "text")
  private String columns;

  // mention table or group of table we need to query on database
  @Lob
  @Column(name = "table_names", columnDefinition = "text")
  private String table;

  // mentuion where query
  @Lob
  @Column(name = "filters", columnDefinition = "text")
  private String filter;

  // this will be used to order the record in seq. make sure combination of keys should be unique
  // for a records
  @Column(name = "partition_column")
  private String partitionColumn;

  // This indiscates how many record we will fetch in one batch
  @Column(name = "batch_size")
  private Integer batchSize;

  // This indiscates how many days we need to sync
  @Column(name = "last_x_days")
  private Integer lastXDays;

  // will push records in this kafka topic
  @Column(name = "kafka_topic_name")
  private String kafkaTopicName;

  // this will be the partition kety in kafka
  @Column(name = "kafka_partition_key")
  private String kafkaPartitionKey;

  // this will be used as a cache key in redis
  @Type(JsonType.class)
  @Column(name = "cache_keys", columnDefinition = "json")
  private List<String> cacheKeys;

  @Column(name = "enabled")
  private Boolean enabled;
}
