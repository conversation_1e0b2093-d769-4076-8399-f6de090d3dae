package com.seera.lumi.yaqeen.api.petromin;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Feign;
import feign.Logger;
import feign.Retryer;
import feign.auth.BasicAuthRequestInterceptor;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class PetrominFeignConfig {

  @Value("${api.petromin.base.url}")
  private String baseUrl;

  @Value("${api.petromin.username}")
  private String username;

  @Value("${api.petromin.password}")
  private String password;

  @Autowired private ObjectMapper objectMapper;

  @Bean
  PetrominClient petrominClient() {
    return Feign.builder()
        .encoder(new JacksonEncoder(objectMapper))
        .decoder(new JacksonDecoder(objectMapper))
        .logLevel(Logger.Level.FULL)
        .retryer(new Retryer.Default(4000L, 10000, 3))
        .requestInterceptor(new BasicAuthRequestInterceptor(username, password))
        .target(PetrominClient.class, baseUrl);
  }
}
