package com.seera.lumi.yaqeen.api.carpro;

import com.seera.lumi.yaqeen.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(
    name = "CarProClient",
    url = "${api.carpro.base.url}",
    dismiss404 = true,
    configuration = FeignConfig.class)
public interface CarProClient {

  @GetMapping("replication/{job-name}")
  ResponseEntity<Object> getAllData(
      @RequestParam("batchNo") Integer batchNo,
      @RequestParam("returnCount") Boolean returnCount,
      @PathVariable("job-name") String jobName);
}
