package com.seera.lumi.yaqeen.api.saferoad;

import com.seera.lumi.yaqeen.api.saferoad.request.SafeRoadLoginRequest;
import com.seera.lumi.yaqeen.api.saferoad.response.SafeRoadLoginResponse;
import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(name = "SafeRoadClient", url = "${api.saferoad.base.url}")
public interface SafeRoadClient {

  @GetMapping("login")
  SafeRoadLoginResponse getSafeRoadToken(@RequestBody SafeRoadLoginRequest loginRequest);

  @GetMapping("vehicles/settings?withloc=1")
  Response getVehicleSettings(@RequestHeader(HttpHeaders.AUTHORIZATION) String token);

  @GetMapping("vehicles/vehiclesMileage")
  Response getVehicleMileage(
      @RequestParam("plateNumbers") String plateNumbers,
      @RequestHeader(HttpHeaders.AUTHORIZATION) String token);
}
