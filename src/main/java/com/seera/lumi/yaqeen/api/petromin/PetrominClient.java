package com.seera.lumi.yaqeen.api.petromin;

import com.seera.lumi.yaqeen.api.petromin.request.PetrominDeliveryNoteRequest;
import com.seera.lumi.yaqeen.api.petromin.response.PetrominDeliveryNoteResponse;
import feign.QueryMap;
import feign.RequestLine;
import org.springframework.stereotype.Component;

import java.net.URI;

@Component
public interface PetrominClient {

  @RequestLine("GET")
  PetrominDeliveryNoteResponse getData(URI url, @QueryMap PetrominDeliveryNoteRequest request);
}
