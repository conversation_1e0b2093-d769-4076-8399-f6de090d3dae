# Email Setup for CSV Reports

## Overview
This application now includes email functionality to automatically send CSV reports to `<EMAIL>` with `<EMAIL>` in CC.

## Configuration

### 1. Email Configuration
Add the following environment variables to your application:

```bash
# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

### 2. Gmail Setup (if using Gmail)
1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a new app password for "Mail"
3. Use the generated app password as `<PERSON><PERSON>_PASSWORD`

## How it Works

### Automatic Email Sending
When any report is generated (like `openAgreementReport()`, `invoiceReport()`, etc.), the system will:
1. Upload the CSV file to Google Drive
2. Automatically send an email to `<EMAIL>` with the CSV file attached
3. CC `<EMAIL>` on the email

### Manual Email Sending
You can also manually send CSV files via email using the REST API:

```bash
curl -X POST \
  http://localhost:8182/api/reports/send-csv-email \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@your-file.csv'
```

## API Endpoints

### Send CSV Email
- **URL**: `POST /api/reports/send-csv-email`
- **Content-Type**: `multipart/form-data`
- **Parameter**: `file` (CSV file)
- **Response**: Success/error message

## Email Content
The email will include:
- **To**: <EMAIL>
- **CC**: <EMAIL>
- **Subject**: "CSV Report: [filename]"
- **Body**: Automated message with timestamp
- **Attachment**: The CSV file

## Error Handling
- If email sending fails, it won't break the Google Drive upload process
- All email errors are logged for debugging
- The system continues to work even if email is not configured

## Testing
To test the email functionality:

1. Set up the email configuration
2. Generate any report using the existing endpoints
3. Check the logs for email sending confirmation
4. Verify the email is received at the specified addresses

## Troubleshooting

### Common Issues:
1. **Authentication failed**: Check your email credentials and app password
2. **Connection timeout**: Verify SMTP host and port settings
3. **File not found**: Ensure the CSV file exists before sending

### Logs to Check:
- Look for "Email sent successfully" or "Failed to send email" messages in the application logs
- Check SMTP connection logs for detailed error information 