# Yaqeen Backend Service

[Home page]()

Build Job : 
[*Dev*](https://jenkins.lumirental.io/job/lease-backend/job/dev/job/core-service/)  | 
[*Staging*](https://jenkins.lumirental.io/job/lease-backend/job/staging/job/core-service/)


## Table of Contents
* [About](#about)
* [API Documentation](#api-docs)
* [Server and Environments](#environments)
* [Develop](#develop)

## About<a id="about"/>

Lumi core yaqeen business is the REST service which is responsible for core domain

## API Documentation<a id="api-docs"/>
[Api Details]() <br>
[Troubleshooting]()

## Servers and Environments <a id="environments"/>
For Server and Environment Details, please check [here]()

### Service Info
| Env | Endpoint                                                    |
|-----|-------------------------------------------------------------|
| Dev | https://api-dev.lumirental.com/yaqeen-core-service/actuator/info        |
| Staging | https://api-staging.lumirental.com/yaqeen-core-service/actuator/info  |
| Prod | https://api.lumirental.com/yaqeen-core-service/actuator/info              |

### Service Health
| Env | Endpoint                                                            |
|-----|---------------------------------------------------------------------|
| Dev | https://api-dev.lumirental.com/yaqeen-core-service/actuator/health   |
| Staging | https://api-staging.lumirental.com/yaqeen-core-service/actuator/health |
| Prod | https://api.lumirental.com/yaqeen-core-service/actuator/health         |


## Develop<a id="develop"/> 
### How to build?
```
mvn clean install
```

### How to run service locally?
```
java -jar target/lease-yaqeen-service-0.0.1.jar
```

### How to confirm that the service is running locally?

Visit: http://localhost:8182/actuator/health

### How to run service in debug mode?
```
java -Xdebug -Xrunjdwp:server=y,transport=dt_socket,address=9999,suspend=n -jar target/lease-yaqeen-service-0.0.1.jar
```

## Release<a id="release"/>
### Dev 
https://argo.dev.lumirental.io/applications/lumi-core-yaqeen-business

### Staging
https://argo.staging.lumirental.io/applications/lumi-core-yaqeen-business

### Prod
https://argo.lumirental.io/applications/lumi-core-yaqeen-business


## Monitor<a id="monitor">

### Loki
[*Dev*](https://grafana.dev.lumirental.io/explore?schemaVersion=1&panes=%7B%22UI3%22:%7B%22datasource%22:%22Jx6g8ACnz%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bservice%3D%5C%22lumi-core-yaqeen-business%5C%22%7D%22,%22queryType%22:%22range%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22Jx6g8ACnz%22%7D,%22editorMode%22:%22builder%22%7D%5D,%22range%22:%7B%22from%22:%22now-30m%22,%22to%22:%22now%22%7D%7D%7D&orgId=1) |
[*Staging*]() |
[*Prod*](https://grafana.lumirental.io/explore?schemaVersion=1&panes=%7B%22cCL%22:%7B%22datasource%22:%22djVed8Cnk%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bservice%3D%5C%22lumi-core-yaqeen-business%5C%22%7D%22,%22queryType%22:%22range%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22djVed8Cnk%22%7D,%22editorMode%22:%22builder%22%7D%5D,%22range%22:%7B%22from%22:%22now-15m%22,%22to%22:%22now%22%7D%7D%7D&orgId=1)


### Service Dashboard
#### Dev
[*Infra*](https://grafana.dev.lumirental.io/d/_gLbd5_izasass/deployments?orgId=1&var-job=k8s-containers-metrics&var-namespace=core&var-deployment=lumi-core-yaqeen-business&var-pod=All&var-Node=All&var-interval=5m) |
[*Business*](https://grafana.dev.lumirental.io/d/LJ_uJAvmg/backend-service-dashboard?orgId=1&refresh=5m&var-datasource=default&var-service=lumi-core-yaqeen-business.core.svc.cluster.local&var-qrep=destination&var-srccluster=All&var-srcns=All&var-srcwl=All&var-dstcluster=All&var-dstns=All&var-dstwl=All) |
[*JVM*](https://grafana.dev.lumirental.io/d/Go50wSTZk/jvm-micrometer?orgId=1&refresh=30s&var-apps=lumi-core-yaqeen-business&var-kubernetes_pod_name=lumi-core-yaqeen-business-deployment-7d97668bb8-qvdb4&var-jvm_memory_pool_heap=All&var-jvm_memory_pool_nonheap=All) |
[*Micrometer*](https://grafana.dev.lumirental.io/d/vwgu1P7Mz/micrometer?orgId=1&var-apps=lumi-core-yaqeen-business&var-kubernetes_pod_name=lumi-core-yaqeen-business-deployment-7d97668bb8-hf6n7&var-hikaricp=HikariPool-1&var-memory_pool_heap=All&var-memory_pool_nonheap=All)
#### Prod
[*Infra*](https://grafana.lumirental.io/d/bFv9Mtm7k/deployments?orgId=1&var-job=k8s-containers-metrics&var-namespace=core&var-deployment=lumi-core-yaqeen-business&var-pod=All&var-Node=All&var-interval=5m&from=now-1h&to=now) |
[*Business*](https://grafana.lumirental.io/d/LJ_uJAvmk/backend-service-dashboard?orgId=1&refresh=5m&var-datasource=default&var-service=lumi-core-yaqeen-business.core.svc.cluster.local&var-qrep=destination&var-srccluster=All&var-srcns=All&var-srcwl=All&var-dstcluster=All&var-dstns=All&var-dstwl=All) |
[*JVM*](https://grafana.lumirental.io/d/LDdwKpinz/jvm-micrometer?orgId=1&refresh=30s&var-apps=lumi-core-yaqeen-business&var-kubernetes_pod_name=lumi-core-yaqeen-business-deployment-5ddb65f54d-4kvs9&var-jvm_memory_pool_heap=All&var-jvm_memory_pool_nonheap=All) |
[*Micrometer*](https://grafana.lumirental.io/d/GtolFtink/micrometer?orgId=1&var-apps=lumi-core-yaqeen-business&var-kubernetes_pod_name=lumi-core-yaqeen-business-deployment-5ddb65f54d-4kvs9&var-hikaricp=HikariPool-1&var-memory_pool_heap=All&var-memory_pool_nonheap=All)


### Dependencies Dashboard
#### Redis
[*Dev*](https://grafana.dev.lumirental.io/d/aXPBKto6ik/redis?orgId=1&refresh=30s) |
[*Staging*]() |
[*Prod*](https://grafana.lumirental.io/d/3VBG5tink/redis?orgId=1)
#### MySql
[*Dev*](https://grafana.dev.lumirental.io/d/MQWgroiiz/mysql?orgId=1&refresh=10s) |
[*Staging*]() |
[*Prod*](https://grafana.lumirental.io/d/V-FW5tink/mysql?orgId=1&refresh=10s&var-host=mysql-lumi-ultra-prod-apps&var-interval=$__auto_interval_interval)
#### Kafka
[*Dev*](https://grafana.dev.lumirental.io/d/jwPKIsniz/kafka?orgId=1&refresh=5s) |
[*Staging*]() |
[*Prod*](https://grafana.lumirental.io/d/ceZScpinz/kafka?orgId=1&refresh=5s)